<?php //vince created this file

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        
        Schema::create('additional_charges', function (Blueprint $table) {
            $table->id();
            $table->foreignId('invoice_id')->constrained()->onDelete('cascade');
            $table->foreignId('order_id')->nullable()->constrained()->onDelete('set null');//vince added
            $table->string('description')->nullable();
            $table->decimal('amount', 10, 2);
            $table->timestamps();
        });

        
        // Seed additional charges
        DB::table('additional_charges')->insert([
            [
                'invoice_id' => 1,
                'description' => 'Room service',
                'amount' => 50.00,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'invoice_id' => 2,
                'description' => 'Late checkout',
                'amount' => 25.75,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'invoice_id' => 3,
                'description' => 'Mini bar usage',
                'amount' => 30.00,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'invoice_id' => 4,
                'description' => 'Parking fee',
                'amount' => 20.00,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'invoice_id' => 5,
                'description' => 'Airport transfer',
                'amount' => 15.00,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
        
        
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('additional_charges');
    }
};
