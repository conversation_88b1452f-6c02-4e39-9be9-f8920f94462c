<?php

namespace App\Http\Controllers;

use App\Models\Hotel;
use App\Models\SubscriptionPlan;
use App\Models\Subscription;
use App\Models\BillingInvoice;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SubscriptionController extends Controller
{
    /**
     * Display subscription plans for hotel selection.
     */
    public function plans()
    {
        $plans = SubscriptionPlan::active()->ordered()->get();
        $currentHotel = get_current_hotel();
        
        return view('admin.subscriptions.plans', compact('plans', 'currentHotel'));
    }

    /**
     * Display current subscription details.
     */
    public function current()
    {
        $currentHotel = get_current_hotel();
        
        if (!$currentHotel) {
            return redirect()->route('unauthorized');
        }

        $subscription = $currentHotel->currentSubscription;
        $invoices = $currentHotel->billingInvoices()->latest()->take(10)->get();
        
        return view('admin.subscriptions.current', compact('currentHotel', 'subscription', 'invoices'));
    }

    /**
     * Show subscription upgrade/change form.
     */
    public function upgrade(SubscriptionPlan $plan)
    {
        $currentHotel = get_current_hotel();
        
        if (!$currentHotel) {
            return redirect()->route('unauthorized');
        }

        $currentSubscription = $currentHotel->currentSubscription;
        
        // Check if plan can accommodate hotel
        if (!$plan->canAccommodateHotel($currentHotel)) {
            return back()->with('error', 'This plan cannot accommodate your current hotel setup. Please contact support.');
        }

        return view('admin.subscriptions.upgrade', compact('currentHotel', 'currentSubscription', 'plan'));
    }

    /**
     * Process subscription upgrade/change.
     */
    public function processUpgrade(Request $request, SubscriptionPlan $plan)
    {
        $request->validate([
            'billing_cycle' => 'required|in:monthly,yearly',
        ]);

        $currentHotel = get_current_hotel();
        
        if (!$currentHotel) {
            return redirect()->route('unauthorized');
        }

        try {
            // Calculate pricing
            $amount = $plan->price;
            if ($request->billing_cycle === 'yearly') {
                $amount = $plan->yearly_price;
            }

            // Create new subscription
            $subscription = Subscription::create([
                'hotel_id' => $currentHotel->id,
                'subscription_plan_id' => $plan->id,
                'status' => 'active',
                'amount' => $amount,
                'billing_cycle' => $request->billing_cycle,
                'current_period_start' => now(),
                'current_period_end' => $request->billing_cycle === 'monthly' 
                    ? now()->addMonth() 
                    : now()->addYear(),
            ]);

            // Update hotel's current subscription
            $currentHotel->update(['current_subscription_id' => $subscription->id]);

            // Cancel old subscription if exists
            if ($currentHotel->currentSubscription && $currentHotel->currentSubscription->id !== $subscription->id) {
                $currentHotel->currentSubscription->cancel();
            }

            // Create invoice
            $this->createInvoice($subscription);

            log_activity("Upgraded to {$plan->name} plan", 'Subscription');

            return redirect()->route('admin.subscription.current')
                ->with('success', "Successfully upgraded to {$plan->name} plan!");

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to upgrade subscription. Please try again.');
        }
    }

    /**
     * Cancel subscription.
     */
    public function cancel()
    {
        $currentHotel = get_current_hotel();
        
        if (!$currentHotel || !$currentHotel->currentSubscription) {
            return redirect()->route('unauthorized');
        }

        $currentHotel->currentSubscription->cancel();
        
        log_activity('Cancelled subscription', 'Subscription');

        return redirect()->route('admin.subscription.current')
            ->with('success', 'Subscription cancelled successfully. You can continue using the service until the end of your billing period.');
    }

    /**
     * Resume cancelled subscription.
     */
    public function resume()
    {
        $currentHotel = get_current_hotel();
        
        if (!$currentHotel || !$currentHotel->currentSubscription) {
            return redirect()->route('unauthorized');
        }

        $currentHotel->currentSubscription->resume();
        
        log_activity('Resumed subscription', 'Subscription');

        return redirect()->route('admin.subscription.current')
            ->with('success', 'Subscription resumed successfully!');
    }

    /**
     * Display billing history.
     */
    public function billing()
    {
        $currentHotel = get_current_hotel();
        
        if (!$currentHotel) {
            return redirect()->route('unauthorized');
        }

        $invoices = $currentHotel->billingInvoices()->with('subscription.plan')->latest()->paginate(15);
        
        return view('admin.subscriptions.billing', compact('currentHotel', 'invoices'));
    }

    /**
     * Download invoice PDF.
     */
    public function downloadInvoice(BillingInvoice $invoice)
    {
        $currentHotel = get_current_hotel();
        
        if (!$currentHotel || $invoice->hotel_id !== $currentHotel->id) {
            abort(403);
        }

        // Here you would generate and return PDF
        // For now, return a simple response
        return response()->json([
            'message' => 'PDF generation would be implemented here',
            'invoice' => $invoice
        ]);
    }

    /**
     * Create invoice for subscription.
     */
    private function createInvoice(Subscription $subscription)
    {
        $lineItems = [
            [
                'description' => $subscription->plan->name . ' Plan (' . ucfirst($subscription->billing_cycle) . ')',
                'quantity' => 1,
                'unit_price' => $subscription->amount,
                'total' => $subscription->amount,
            ]
        ];

        $subtotal = $subscription->amount;
        $taxRate = 0.10; // 10% tax
        $taxAmount = $subtotal * $taxRate;
        $total = $subtotal + $taxAmount;

        return BillingInvoice::create([
            'hotel_id' => $subscription->hotel_id,
            'subscription_id' => $subscription->id,
            'status' => 'sent',
            'subtotal' => $subtotal,
            'tax_amount' => $taxAmount,
            'total_amount' => $total,
            'invoice_date' => now(),
            'due_date' => now()->addDays(30),
            'line_items' => $lineItems,
        ]);
    }
}
