<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('room_types', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('description')->nullable();//vince orig text
            //room rate
            $table->decimal('rate', 10, 2)->default(0);
            $table->timestamps();
        });

        //insert room types
        DB::table('room_types')->insert([
            ['name' => 'Single', 'description' => 'Single Room', 'rate' => 1000.00],
            ['name' => 'Double', 'description' => 'Double Room', 'rate' => 1500.00],
            ['name' => 'Family', 'description' => 'Family Room', 'rate' => 2000.00],
            ['name' => 'Suite', 'description' => 'Suite Room', 'rate' => 2500.00],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('room_types');
    }
};
