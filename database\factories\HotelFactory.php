<?php

namespace Database\Factories;

use App\Models\Hotel;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Hotel>
 */
class HotelFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Hotel::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->company . ' Hotel';
        
        return [
            'name' => $name,
            'slug' => Str::slug($name),
            'email' => $this->faker->unique()->safeEmail(),
            'phone' => $this->faker->phoneNumber(),
            'address' => $this->faker->address(),
            'tin' => $this->faker->numerify('###-###-###-###'),
            'description' => $this->faker->sentence(),
            'logo' => null,
            'website' => $this->faker->url(),
            'status' => 'active',
            'settings' => null,
            'trial_ends_at' => now()->addDays(30),
            'subscription_ends_at' => null,
            'current_subscription_id' => null,
            'custom_domain' => null,
            'subdomain' => Str::slug($name),
            'brand_color' => $this->faker->hexColor(),
            'brand_logo' => null,
        ];
    }

    /**
     * Indicate that the hotel is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'inactive',
        ]);
    }

    /**
     * Indicate that the hotel is suspended.
     */
    public function suspended(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'suspended',
        ]);
    }

    /**
     * Indicate that the hotel has an active subscription.
     */
    public function withActiveSubscription(): static
    {
        return $this->state(fn (array $attributes) => [
            'subscription_ends_at' => now()->addMonths(1),
        ]);
    }
}
