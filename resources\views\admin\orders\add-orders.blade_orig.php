@include('layout.admin-header')

@php
    $currencySymbol = config('currency.symbol');
    

@endphp

<style>
    .disabled-button {
        background-color: #d6d6d6;
        /* Light grey background */
        color: #a0a0a0;
        /* Grey text color */
        cursor: not-allowed;
        /* Show a 'not-allowed' cursor */
        border-color: #b0b0b0;
        /* Grey border */
    }

    .disabled-button:hover {
        background-color: #d6d6d6;
        /* Keep the same color on hover */
    }
</style>


<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        @include('layout.admin-sidebar')
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                @include('layout.admin-topbar')
                <!-- End of Topbar -->
                <!-- Begin Page Content -->
                <div class="container-fluid">
                    @include('shared.success-message')
                    @include('shared.error-message')

                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">Orders - Add Order</h6>
                            <a href="{{ route('admin.orders') }}" class="btn btn-danger">BACK</a>
                        </div>

                        <div class="card-body row">
                            <!-- Left Column: Receipt View -->
                            <div class="col-md-6">
                                <div id="paymongo_qr_code"></div>
                                <h5>Receipt</h5>
                                <div class="receipt-view p-3 border rounded" id="receipt_view">
                                    <!-- Receipt Items will appear here -->
                                    <!-- Add a container to hold the QR code -->
                                </div>
                                <hr>
                                <!-- Customer Info and Order Details -->
                                <div class="mt-4">
                                    <div class="form-group">
                                        <label for="customer_name">Customer Name</label>
                                        <input class="form-control" id="customer_name" name="customer_name"
                                            placeholder="--Optional--" autocomplete="off">
                                    </div>

                                    <button id="submitButton" type="button" class="btn btn-primary btn-block"
                                        onclick="submitOrder()">Create Order</button>

                                    {{-- PAYMONGO BUTTON --}}
                                    <button id="paymongoButton" type="button" class="btn btn-primary btn-block"
                                        onclick="payfood_order()">Pay with Paymongo</button>
                                </div>

                                <div class="form-group">
                                    <label for="received_amount">Received Amount</label>
                                    <input type="number" id="received_amount" class="form-control" min="0"
                                        oninput="calculateChange()" required>
                                </div>
                                <div class="form-group">
                                    <label for="order_items_total">Total</label>
                                    <input type="text" id="order_items_total" class="form-control" readonly>
                                </div>
                                <div class="form-group">
                                    <label for="change_amount">Change</label>
                                    <input type="text" id="change_amount" class="form-control" readonly>
                                </div>

                                <hr>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="" id="charge_to_room"
                                        onchange="toggleRoomCharge()">
                                    <label class="form-check-label" for="charge_to_room">
                                        Charge to Room
                                    </label>
                                </div>
                                <div class="form-group mt-3" id="room_number_field" style="display:none;">
                                    <label for="room_number">Room Number</label>
                                    <select id="room_number" class="form-control">
                                        <!-- Room options will be dynamically populated here -->
                                    </select>
                                </div>
                            </div>

                            <!-- Right Column: Menu Items -->
                            <div class="col-md-6">
                                <h5>Menu Items</h5>
                                <div class="menu-items-grid">
                                    <!-- Dynamically populated buttons for each menu item -->
                                    @foreach ($menu_list as $menu)
                                        <button type="button" class="btn btn-secondary mb-2 menu-item-btn"
                                            onclick="addMenuItem({{ $menu->id }}, '{{ $menu->name }}', {{ $menu->price }})">
                                            {{ $menu->name }} -
                                            {{ $currencySymbol }}{{ number_format($menu->price, 2) }}
                                        </button>
                                    @endforeach
                                </div>


                            </div>
                        </div>
                    </div>
                </div>
                <!-- End of Main Content -->

                <!-- Footer -->
                @include('layout.admin-footer')
                <!-- End of Footer -->

            </div>
            <!-- End of Content Wrapper -->

        </div>
        <!-- End of Page Wrapper -->

        <!-- Scroll to Top Button-->
        <a class="scroll-to-top rounded" href="#page-top">
            <i class="fas fa-angle-up"></i>
        </a>

        <!-- Bootstrap core JavaScript-->
        <script src="{{ asset('assets/js/jquery.min.js') }}"></script>
        <script src="{{ asset('assets/js/bootstrap.bundle.min.js') }}"></script>
        <script src="{{ asset('assets/js/jquery.easing.min.js') }}"></script>
        <script src="{{ asset('assets/js/sb-admin-2.min.js') }}"></script>
        <script src="{{ asset('assets/js/datetimepicker.js') }}"></script>

        <script>
            let orderItems = [];

            function addMenuItem(id, name, price) {
                const existingItem = orderItems.find(item => item.id === id);
                if (existingItem) {
                    existingItem.quantity++;
                } else {
                    orderItems.push({
                        id,
                        name,
                        price,
                        quantity: 1
                    });
                }
                updateReceiptView();
            }

            function updateReceiptView() {
                const receiptView = document.getElementById('receipt_view');
                receiptView.innerHTML = '';

                let total = 0;
                orderItems.forEach((item, index) => {
                    total += item.price * item.quantity;
                    const itemRow = document.createElement('div');
                    itemRow.className = 'd-flex justify-content-between mb-2';

                    itemRow.innerHTML = `
                        <div>${item.name} x${item.quantity}</div>
                        <div>{{ $currencySymbol }}${(item.price * item.quantity).toFixed(2)}</div>
                        <button type="button" class="btn btn-danger btn-sm" onclick="removeMenuItem(${index})">Remove</button>
                    `;

                    receiptView.appendChild(itemRow);
                });

                document.getElementById('order_items_total').value = total.toFixed(2);
                calculateChange();
            }

            function removeMenuItem(index) {
                orderItems.splice(index, 1);
                updateReceiptView();
            }

            function calculateChange() {
                const total = parseFloat(document.getElementById('order_items_total').value) || 0;
                const received = parseFloat(document.getElementById('received_amount').value) || 0;
                const change = received - total;
                document.getElementById('change_amount').value = change.toFixed(2);

                // Disable the submit button if the change is less than 0
                const submitButton = document.getElementById('submitButton');
                if (change < 0) {
                    submitButton.disabled = true;
                    submitButton.classList.add('disabled-button'); // Add CSS class for disabled state
                } else {
                    submitButton.disabled = false;
                    submitButton.classList.remove('disabled-button'); // Remove CSS class
                }
            }

            function toggleRoomCharge() {
                const roomChargeChecked = document.getElementById('charge_to_room').checked;
                const roomNumberField = document.getElementById('room_number_field');
                roomNumberField.style.display = roomChargeChecked ? 'block' : 'none';


                if (roomChargeChecked) {
                    fetchRoomOptions();
                } else {
                    const roomNumberSelect = document.getElementById('room_number');
                    roomNumberSelect.innerHTML = ''; // Clear the room options if unchecked
                }
            }

            function fetchRoomOptions() {
                $.ajax({
                    url: "{{ route('get.available.rooms.forRestaurant') }}",
                    method: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        startDate: '{{ now()->toDateString() }}'
                    },
                    success: function(response) {
                        const roomNumberSelect = document.getElementById('room_number');
                        roomNumberSelect.innerHTML = '';

                        response.rooms.forEach(room => {
                            const option = document.createElement('option');
                            option.value = room.id;
                            option.text = `Room ${room.number} (${room.type})`;
                            roomNumberSelect.appendChild(option);
                        });
                    },
                    error: function() {
                        alert('Error fetching available rooms.');
                    }
                });
            }
            

            function payfood_order() {
                const customerName = document.getElementById('customer_name').value;
                const roomCharge = document.getElementById('charge_to_room').checked;
                const roomNumber = document.getElementById('room_number').value;
                const totalAmount = parseFloat(document.getElementById('order_items_total').value) || 0;
                const receivedAmount = parseFloat(document.getElementById('received_amount').value) || 0;
                const changeAmount = parseFloat(document.getElementById('change_amount').value) || 0;

                const items = orderItems.map(item => ({
                    menu_id: item.id,
                    quantity: item.quantity
                }));

                const orderData = {
                    customer_name: customerName,
                    items: items,
                    total_amount: totalAmount,
                    received_amount: receivedAmount,
                    change_amount: changeAmount,
                    room_charge: roomCharge,
                    room_number: roomNumber
                };

                $.ajax({
                    url: "{{ route('admin.payments.paymongo.payfood_order') }}",
                    method: 'POST', // Changed from GET to POST
                    data: {
                        _token: '{{ csrf_token() }}',
                        orderData: JSON.stringify(orderData)
                    },
                    success: function(response) {
                        // Display the QR code
                        console.log(response); // Log the entire response
                        console.log(response.qr_code);
                        $('#paymongo_qr_code').empty().append(`
                <img src="data:image/svg+xml;base64,${response.qr_code}" alt="QR Code" / style="height:300px; width:300px">
                <p>CHECKOUT URL: <a href="${response.redirect_url}" target="_blank">${response.redirect_url}</a></p>
            `);



                    },
                    error: function(xhr) {
                        alert('Error submitting order: ' + xhr.responseText);
                    }
                });
            }


            function submitOrder() {
                const customerName = document.getElementById('customer_name').value;
                const roomCharge = document.getElementById('charge_to_room').checked; // Send boolean directly
                const roomNumber = document.getElementById('room_number').value;
                const totalAmount = parseFloat(document.getElementById('order_items_total').value) || 0;
                const receivedAmount = parseFloat(document.getElementById('received_amount').value) || 0;
                const changeAmount = parseFloat(document.getElementById('change_amount').value) || 0;

                // Prepare the order items data
                const items = orderItems.map(item => ({
                    menu_id: item.id,
                    quantity: item.quantity
                }));

                // Prepare data to send to server
                const orderData = {
                    customer_name: customerName,
                    items: items,
                    total_amount: totalAmount,
                    received_amount: receivedAmount,
                    change_amount: changeAmount,
                    room_charge: roomCharge, // Ensure this is a boolean
                    room_number: roomNumber
                };

                // Send data to server
                $.ajax({
                    url: "{{ route('admin.order.store') }}",
                    method: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        orderData: JSON.stringify(orderData) // Send as JSON
                    },
                    success: function(response) {
                        // Handle successful response (e.g., display a success message or redirect)
                        alert('Order submitted successfully!');
                        window.location.reload(); // Optionally, reload the page or redirect
                    },
                    error: function(xhr) {
                        // Handle error response
                        alert('Error submitting order: ' + xhr.responseText);
                    }
                });
            }
        </script>

</body>

</html>
