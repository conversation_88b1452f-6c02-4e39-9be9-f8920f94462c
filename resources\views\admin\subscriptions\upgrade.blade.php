@include('layout.admin-header')

<body id="page-top">
    <!-- Page Wrapper -->
    <div id="wrapper" data-bs-theme="light">
        <!-- Sidebar -->
        @include('layout.admin-sidebar')
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">
            <!-- Main Content -->
            <div id="content">
                <!-- Topbar -->
                @include('layout.admin-topbar')
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    @include('shared.success-message')
                    @include('shared.error-message')

                    <!-- Page Heading -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">Upgrade Subscription</h1>
                        <a href="{{ route('admin.subscription.current') }}" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
                            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Current Plan
                        </a>
                    </div>

                    <!-- Current vs New Plan Comparison -->
                    <div class="row">
                        <!-- Current Plan -->
                        <div class="col-lg-6">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-secondary">Current Plan</h6>
                                </div>
                                <div class="card-body">
                                    @if($currentSubscription)
                                        <h4 class="text-secondary">{{ $currentSubscription->plan->name }}</h4>
                                        <p class="text-muted">{{ $currentSubscription->plan->description }}</p>
                                        
                                        <div class="pricing mb-3">
                                            <h3 class="text-secondary">${{ number_format($currentSubscription->amount, 2) }}</h3>
                                            <small class="text-muted">per {{ $currentSubscription->billing_cycle }}</small>
                                        </div>

                                        <ul class="list-unstyled">
                                            @foreach($currentSubscription->plan->features as $feature)
                                            <li class="mb-2">
                                                <i class="fas fa-check text-success"></i> {{ $feature }}
                                            </li>
                                            @endforeach
                                        </ul>

                                        <div class="mt-3">
                                            <small class="text-muted">
                                                <strong>Limits:</strong><br>
                                                Rooms: {{ $currentSubscription->plan->max_rooms ?: 'Unlimited' }}<br>
                                                Users: {{ $currentSubscription->plan->max_users ?: 'Unlimited' }}
                                            </small>
                                        </div>
                                    @else
                                        <div class="text-center py-4">
                                            <i class="fas fa-times-circle fa-3x text-gray-300 mb-3"></i>
                                            <h5 class="text-gray-600">No Current Plan</h5>
                                            <p class="text-muted">You don't have an active subscription.</p>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- New Plan -->
                        <div class="col-lg-6">
                            <div class="card shadow mb-4 border-primary">
                                <div class="card-header py-3 bg-primary text-white">
                                    <h6 class="m-0 font-weight-bold">
                                        <i class="fas fa-star"></i> Upgrading to {{ $plan->name }}
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <h4 class="text-primary">{{ $plan->name }}</h4>
                                    <p class="text-muted">{{ $plan->description }}</p>
                                    
                                    <div class="pricing mb-3">
                                        <h3 class="text-primary">{{ $plan->formatted_price }}</h3>
                                        <small class="text-muted">per {{ $plan->billing_cycle }}</small>
                                        @if($plan->yearly_price < ($plan->price * 12))
                                            <br><small class="text-success">
                                                <i class="fas fa-tag"></i> Save with yearly billing!
                                            </small>
                                        @endif
                                    </div>

                                    <ul class="list-unstyled">
                                        @foreach($plan->features as $feature)
                                        <li class="mb-2">
                                            <i class="fas fa-check text-success"></i> {{ $feature }}
                                        </li>
                                        @endforeach
                                    </ul>

                                    <div class="mt-3">
                                        <small class="text-muted">
                                            <strong>Limits:</strong><br>
                                            Rooms: {{ $plan->max_rooms ?: 'Unlimited' }}<br>
                                            Users: {{ $plan->max_users ?: 'Unlimited' }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Upgrade Form -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Complete Your Upgrade</h6>
                        </div>
                        <div class="card-body">
                            <form action="{{ route('admin.subscription.process-upgrade', $plan) }}" method="POST">
                                @csrf
                                
                                <!-- Billing Cycle Selection -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="billing_cycle" class="font-weight-bold">Choose Billing Cycle</label>
                                            <div class="mt-2">
                                                <div class="custom-control custom-radio">
                                                    <input type="radio" id="monthly" name="billing_cycle" value="monthly" class="custom-control-input" checked>
                                                    <label class="custom-control-label" for="monthly">
                                                        <strong>Monthly - ${{ number_format($plan->price, 2) }}</strong>
                                                        <br><small class="text-muted">Billed monthly</small>
                                                    </label>
                                                </div>
                                                <div class="custom-control custom-radio mt-2">
                                                    <input type="radio" id="yearly" name="billing_cycle" value="yearly" class="custom-control-input">
                                                    <label class="custom-control-label" for="yearly">
                                                        <strong>Yearly - ${{ number_format($plan->yearly_price, 2) }}</strong>
                                                        @if($plan->yearly_price < ($plan->price * 12))
                                                            <span class="badge badge-success">Save ${{ number_format(($plan->price * 12) - $plan->yearly_price, 2) }}</span>
                                                        @endif
                                                        <br><small class="text-muted">Billed annually</small>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <!-- Upgrade Summary -->
                                        <div class="card bg-light">
                                            <div class="card-body">
                                                <h6 class="font-weight-bold">Upgrade Summary</h6>
                                                <div class="d-flex justify-content-between">
                                                    <span>Plan:</span>
                                                    <span class="font-weight-bold">{{ $plan->name }}</span>
                                                </div>
                                                <div class="d-flex justify-content-between">
                                                    <span>Billing:</span>
                                                    <span id="billing-display" class="font-weight-bold">Monthly</span>
                                                </div>
                                                <div class="d-flex justify-content-between">
                                                    <span>Amount:</span>
                                                    <span id="amount-display" class="font-weight-bold text-primary">${{ number_format($plan->price, 2) }}</span>
                                                </div>
                                                <hr>
                                                <div class="d-flex justify-content-between">
                                                    <span class="font-weight-bold">Total:</span>
                                                    <span id="total-display" class="font-weight-bold text-primary">${{ number_format($plan->price, 2) }}</span>
                                                </div>
                                                <small class="text-muted">
                                                    <i class="fas fa-info-circle"></i> 
                                                    Changes take effect immediately
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Terms and Conditions -->
                                <div class="form-group mt-4">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="terms" required>
                                        <label class="custom-control-label" for="terms">
                                            I agree to the <a href="#" target="_blank">Terms of Service</a> and understand that my subscription will be upgraded immediately.
                                        </label>
                                    </div>
                                </div>

                                <!-- Action Buttons -->
                                <div class="form-group mt-4">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-credit-card"></i> Upgrade to {{ $plan->name }}
                                    </button>
                                    <a href="{{ route('admin.subscription.current') }}" class="btn btn-secondary btn-lg ml-2">
                                        <i class="fas fa-times"></i> Cancel
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Important Notes -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-warning">
                                <i class="fas fa-exclamation-triangle"></i> Important Information
                            </h6>
                        </div>
                        <div class="card-body">
                            <ul class="mb-0">
                                <li>Your upgrade will take effect immediately upon confirmation.</li>
                                <li>You will be charged the prorated amount for the remainder of your current billing period.</li>
                                <li>Your next billing date will remain the same.</li>
                                <li>You can downgrade or cancel your subscription at any time from your account settings.</li>
                                <li>If you have any questions, please contact our support team.</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <!-- /.container-fluid -->
            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            @include('layout.admin-footer')
            <!-- End of Footer -->
        </div>
        <!-- End of Content Wrapper -->
    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <script>
        // Update pricing display when billing cycle changes
        document.addEventListener('DOMContentLoaded', function() {
            const monthlyRadio = document.getElementById('monthly');
            const yearlyRadio = document.getElementById('yearly');
            const billingDisplay = document.getElementById('billing-display');
            const amountDisplay = document.getElementById('amount-display');
            const totalDisplay = document.getElementById('total-display');
            
            const monthlyPrice = {{ $plan->price }};
            const yearlyPrice = {{ $plan->yearly_price }};
            
            function updateDisplay() {
                if (monthlyRadio.checked) {
                    billingDisplay.textContent = 'Monthly';
                    amountDisplay.textContent = '$' + monthlyPrice.toFixed(2);
                    totalDisplay.textContent = '$' + monthlyPrice.toFixed(2);
                } else if (yearlyRadio && yearlyRadio.checked) {
                    billingDisplay.textContent = 'Yearly';
                    amountDisplay.textContent = '$' + yearlyPrice.toFixed(2);
                    totalDisplay.textContent = '$' + yearlyPrice.toFixed(2);
                }
            }
            
            monthlyRadio.addEventListener('change', updateDisplay);
            yearlyRadio.addEventListener('change', updateDisplay);
        });
    </script>

    <!-- Include Scripts -->
    @include('layout.scripts')
</body>
