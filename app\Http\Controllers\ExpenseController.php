<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Expense;

class ExpenseController extends Controller
{
    public function index()
    {
        $expenses = Expense::all();
        return view('admin.expenses.view-expenses', compact('expenses'));
    }

    public function add()
    {
        return view('admin.expenses.add-expenses');
    }

    public function store(Request $request)
    {
        $request->validate([
            'description' => 'required',
            'amount' => 'required|numeric',
            'expense_type' => 'required',
        ]);

        //Create expense with date nw
        $expense = new Expense();
        $expense->description = $request->description;
        $expense->amount = $request->amount;
        $expense->expense_type = $request->expense_type;
        $expense->expense_date = now();
        $expense->created_by = get_user_email();
        $expense->save();

        return redirect()->route('admin.expenses')
            ->with('success', 'Expense created successfully.');
    }

    public function edit($id)
    {
        $expense = Expense::find($id);
        return view('admin.expenses.edit-expenses', compact('expense'));
    }
    

    public function update(Request $request, $id)
    {
        $request->validate([
            'description' => 'required',
            'amount' => 'required|numeric',
            'expense_type' => 'required',
            'expense_date' => 'required|date',
        ]);

        //Update expense
        $expense = Expense::find($id);
        $expense->description = $request->description;
        $expense->amount = $request->amount;
        $expense->expense_type = $request->expense_type;
        $expense->expense_date = $request->expense_date;
        $expense->save();


        return redirect()->route('admin.expenses')
            ->with('success', 'Expense updated successfully');
    }

    public function destroy(Expense $expense)
    {
        $expense->delete();

        return redirect()->route('admin.expenses')
            ->with('success', 'Expense deleted successfully');
    }
}
