<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">
    <title>Hotel Registration - Hotel Management SaaS</title>
    <!-- Custom fonts for this template-->
    <link href="{{ asset('assets/css/all.min.css') }}" rel="stylesheet" type="text/css">
    <link href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i" rel="stylesheet">
    <!-- Custom styles for this template-->
    <link href="{{ asset('assets/css/sb-admin-2.min.css') }}" rel="stylesheet">
</head>

<body class="bg-gradient-primary">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-xl-8 col-lg-10 col-md-12">
                <div class="card o-hidden border-0 shadow-lg my-5">
                    <div class="card-body p-0">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="p-5">
                                    <div class="text-center">
                                        <h1 class="h4 text-gray-900 mb-4">Start Your Hotel Management Journey</h1>
                                        <p class="mb-4">Join thousands of hotels using our platform to manage their operations efficiently.</p>
                                    </div>
                                    
                                    @if(session('success'))
                                        <div class="alert alert-success">
                                            {{ session('success') }}
                                        </div>
                                    @endif

                                    @if($errors->any())
                                        <div class="alert alert-danger">
                                            <ul class="mb-0">
                                                @foreach($errors->all() as $error)
                                                    <li>{{ $error }}</li>
                                                @endforeach
                                            </ul>
                                        </div>
                                    @endif

                                    <form action="{{ route('hotel.register') }}" method="POST">
                                        @csrf
                                        
                                        <div class="row">
                                            <div class="col-md-6">
                                                <h5 class="text-primary mb-3">Hotel Information</h5>
                                                
                                                <div class="form-group">
                                                    <input type="text" name="name" class="form-control form-control-user @error('name') is-invalid @enderror" 
                                                           placeholder="Hotel Name" value="{{ old('name') }}" required>
                                                    @error('name')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>

                                                <div class="form-group">
                                                    <input type="email" name="email" class="form-control form-control-user @error('email') is-invalid @enderror" 
                                                           placeholder="Hotel Email" value="{{ old('email') }}" required>
                                                    @error('email')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>

                                                <div class="form-group">
                                                    <input type="text" name="phone" class="form-control form-control-user @error('phone') is-invalid @enderror" 
                                                           placeholder="Phone Number" value="{{ old('phone') }}">
                                                    @error('phone')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>

                                                <div class="form-group">
                                                    <textarea name="address" class="form-control @error('address') is-invalid @enderror" 
                                                              placeholder="Hotel Address" rows="3">{{ old('address') }}</textarea>
                                                    @error('address')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <h5 class="text-primary mb-3">Admin Account</h5>
                                                
                                                <div class="form-group">
                                                    <input type="text" name="admin_name" class="form-control form-control-user @error('admin_name') is-invalid @enderror" 
                                                           placeholder="Admin Full Name" value="{{ old('admin_name') }}" required>
                                                    @error('admin_name')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>

                                                <div class="form-group">
                                                    <input type="email" name="admin_email" class="form-control form-control-user @error('admin_email') is-invalid @enderror" 
                                                           placeholder="Admin Email" value="{{ old('admin_email') }}" required>
                                                    @error('admin_email')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>

                                                <div class="form-group">
                                                    <input type="password" name="admin_password" class="form-control form-control-user @error('admin_password') is-invalid @enderror" 
                                                           placeholder="Admin Password" required>
                                                    @error('admin_password')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>

                                                <div class="form-group">
                                                    <input type="password" name="admin_password_confirmation" class="form-control form-control-user" 
                                                           placeholder="Confirm Password" required>
                                                </div>
                                            </div>
                                        </div>

                                        <hr>

                                        <div class="alert alert-info">
                                            <h6><i class="fas fa-gift"></i> What you get:</h6>
                                            <ul class="mb-0">
                                                <li>30-day free trial</li>
                                                <li>Complete hotel management system</li>
                                                <li>Room booking management</li>
                                                <li>Guest management</li>
                                                <li>Payment processing</li>
                                                <li>Reports and analytics</li>
                                                <li>24/7 support</li>
                                            </ul>
                                        </div>

                                        <button type="submit" class="btn btn-primary btn-user btn-block">
                                            <i class="fas fa-rocket"></i> Start Free Trial
                                        </button>
                                    </form>

                                    <hr>
                                    
                                    <div class="text-center">
                                        <a class="small" href="{{ route('login') }}">Already have an account? Login!</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap core JavaScript-->
    <script src="{{ asset('assets/js/jquery.min.js') }}"></script>
    <script src="{{ asset('assets/js/bootstrap.bundle.min.js') }}"></script>
    <!-- Core plugin JavaScript-->
    <script src="{{ asset('assets/js/jquery.easing.min.js') }}"></script>
    <!-- Custom scripts for all pages-->
    <script src="{{ asset('assets/js/sb-admin-2.min.js') }}"></script>
</body>
</html>
