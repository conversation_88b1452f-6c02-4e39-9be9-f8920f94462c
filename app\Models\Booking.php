<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\BelongsToHotel;

class Booking extends Model
{
    use HasFactory, BelongsToHotel;

    protected $table = 'bookings';
    protected $fillable = ['hotel_id', 'guest_first_name', 'guest_last_name', 'guest_email', 'guest_phone', 'guest_id', 'room_id', 'checkin_date', 'checkout_date', 'total_price', 'booking_status', 'booked_by', 'cancelled_by', 'checked_in_by', 'checked_out_by', 'notes'];
    //vince added guest_id, notes
    public function room()
    {
        return $this->belongsTo(Room::class, 'room_id', 'id');
    }

    public function invoices()
    {
        return $this->hasMany(Invoice::class);
    }

    public function events()
    {
        return $this->hasMany(Event::class);
    }

}
