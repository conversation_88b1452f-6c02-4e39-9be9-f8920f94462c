<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Event;
use App\Models\Event_Item;
use App\Models\Menu;
use Carbon\Carbon;
use App\Models\Ingredient;
use App\Models\Grocery_Item;
use App\Models\Stock_Movement;
use App\Models\Expense;

class GroceryController extends Controller
{
    public function groceryView()
    {
        return view('admin.grocery.grocery-view');
    }



    public function search(Request $request)
    {
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);

        $start_date = Carbon::parse($request->input('start_date'))->startOfDay();
        $end_date = Carbon::parse($request->input('end_date'))->endOfDay();

        // Get events within the date range
        $events = Event::whereBetween('start_date', [$start_date, $end_date])->get();

        $groceries = [];
        $totalCost = 0;

        // Loop through each event and get the grocery items
        foreach ($events as $event) {
            $groceryItems = Grocery_Item::where('event_id', $event->id)->get();

            // Loop through each grocery item and get the ingredient details
            foreach ($groceryItems as $item) {
                $ingredient = Ingredient::find($item->ingredient_id);

                if ($ingredient) {
                    $ingredient_id = $ingredient->id; // Use ingredient_id as the key

                    if (isset($groceries[$ingredient_id])) {
                        // Ingredient exists, update quantity, cost, and event_ids
                        $groceries[$ingredient_id]['quantity'] += $item->quantity;
                        $groceries[$ingredient_id]['cost'] += $item->total_cost;

                        if (!in_array($event->id, $groceries[$ingredient_id]['event_ids'])) {
                            $groceries[$ingredient_id]['event_ids'][] = $event->id;
                        }
                    } else {
                        // Ingredient does not exist, add it as a new entry
                        $groceries[$ingredient_id] = [
                            'ingredient_id' => $ingredient->id,
                            'ingredient_name' => $ingredient->ingredient_name,
                            'quantity' => $item->quantity,
                            'unit' => $item->unit,
                            'cost' => $item->total_cost,
                            'supplier' => $ingredient->supplier,
                            'is_bought' => $item->is_bought,
                            'event_ids' => [$event->id], // Initialize event_ids with the current event
                        ];
                    }

                    // Accumulate total cost
                    $totalCost += $item->total_cost;
                }
            }
        }

        // Convert groceries array to indexed array for the view
        $groceries = array_values($groceries);
        $totalCost = round($totalCost, 2);
        $title = 'Groceries for '.$start_date->format('m/d/y H:i').'-'.$end_date->format('m/d/y H:i');
        if (empty($groceries)) {
            session()->flash('error', 'No event requires groceries on those dates');
        }
        return view('admin.grocery.grocery-view', compact('groceries', 'totalCost', 'title'));
    }





    // public function updateStock(Request $request)
    // {
    //     $ingredientName = $request->input('ingredient_name');
    //     $menuId = $request->input('menu_id');
    //     $ingredient = Ingredient::where('ingredient_name', $ingredientName)->first();

    //     if ($ingredient) {
    //         // Increment stock by purchased amount (e.g., 1 unit per purchase)
    //         $ingredient->stock += $ingredient->quantity;
    //         $ingredient->save();

    //         // Update `bought_grocery` column in Event_Item
    //         Event_Item::where('menu_id', $menuId)->update(['bought_grocery' => true]);

    //         return response()->json(['message' => 'Stock updated successfully']);
    //     }

    //     return response()->json(['error' => 'Ingredient not found'], 404);
    // }

    public function updatePurchaseStatus(Request $request)
    {
        $request->validate([
            'ingredient_id' => 'required|integer',
            'event_ids' => 'required|array', // Validate as an array
            'event_ids.*' => 'integer', // Each item in the array should be an integer
            'is_bought' => 'required|integer|in:0,1',  // Validate as 0 or 1
        ]);

        $ingredientId = $request->input('ingredient_id');
        $eventIds = $request->input('event_ids');
        $isBought = $request->input('is_bought');

        // Find all grocery items with the same ingredient_id and any of the event_ids
        $groceryItems = Grocery_Item::where('ingredient_id', $ingredientId)
            ->whereIn('event_id', $eventIds)
            ->get();

        if ($groceryItems->isEmpty()) {
            return response()->json(['success' => false, 'message' => 'No matching records found'], 404);
        }

        // Calculate the total quantity to be added to stock
        $totalQuantity = $groceryItems->sum('quantity');

        // Update is_bought for all matching grocery items
        foreach ($groceryItems as $groceryItem) {
            $groceryItem->is_bought = $isBought;
            $groceryItem->save();

            Expense::create([
                'description' => 'Purchase of ingredient ' . $groceryItem->ingredient->ingredient_name, // Assuming ingredient name is available
                'amount' => $groceryItem->total_cost,
                'expense_type' => 'GROCERY',
                'expense_date' => now(),
                'grocery_item_id' => $groceryItem->id,
                'created_by' => get_user_email(),
            ]);
        }

        // Update stock for the ingredient
        $ingredient = Ingredient::find($ingredientId);
        if ($ingredient) {
            if ($isBought == 1) {
                $ingredient->stock += $totalQuantity; // Increment stock
                Stock_Movement::create([
                    'ingredient_id' => $ingredient->id,
                    'quantity' => $totalQuantity,
                    'movement_type' => 'increment',  // Use 'increment' to indicate stock addition
                    'reason' => 'Purchase confirmed',
                ]);

                

            } else {
                // Decrease stock if not purchased
                $ingredient->stock -= $totalQuantity;
                Stock_Movement::create([
                    'ingredient_id' => $ingredient->id,
                    'quantity' => $totalQuantity,
                    'movement_type' => 'decrement',  // Use 'decrement' to indicate stock removal
                    'reason' => 'Purchase canceled',
                ]);

                foreach ($groceryItems as $groceryItem) {

                // Remove expense record
                Expense::where('grocery_item_id', $groceryItem->id)->delete();
                }
            }
            $ingredient->save();
        }

        // Log activity
        log_activity('Updated Grocery Purchase Status' . '-' . 'Updated purchase status for ingredient ' . $ingredient->ingredient_name, 'Grocery controller');

        return response()->json(['success' => true, 'message' => 'Purchase status updated for all records']);
    }
}
