@include('layout.admin-header')
@php
    $currencySymbol = config('currency.symbol');
@endphp

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        @include('layout.admin-sidebar')
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                @include('layout.admin-topbar')
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    @include('shared.success-message')
                    @include('shared.error-message')

                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Booking Invoices</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="myRequests" class="table table-bordered table-striped" width="100%"
                                    cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>Inv ID</th>
                                            <th>Book ID</th>
                                            <th>Guest Name</th>
                                            <th>Room</th>
                                            <th>Room Type</th>
                                            <th>Total Amount</th>
                                            <th>Invoice Date</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                            <th>QR Code</th> <!-- New Column for QR Codes -->
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($invoices as $invoice)
                                            <tr>
                                                <td>{{ $invoice->id }}</td>
                                                <td>{{ $invoice->booking_id }}</td>
                                                <td>
                                                    @if ($invoice->booking)
                                                        {{ $invoice->booking->guest_first_name . ' ' . $invoice->booking->guest_last_name }}
                                                    @else
                                                        N/A
                                                    @endif
                                                </td>
                                                <td>
                                                    @if ($invoice->booking)
                                                        {{ $invoice->booking->room->room_number }}
                                                    @else
                                                        N/A
                                                    @endif
                                                </td>
                                                <td>
                                                    @if ($invoice->booking)
                                                        {{ $invoice->booking->room->roomType->name }}
                                                    @else
                                                        N/A
                                                    @endif

                                                </td>
                                                <td style="text-align:right;">{{ $currencySymbol . number_format($invoice->total_amount,2) }}</td>
                                                <td class="date" data-date="{{ $invoice->invoice_date }}"></td>
                                                <td>{{ $invoice->status }}</td>
                                                <!-- Moment -->
                                                <script src="{{ asset('assets/js/moment.min.js') }}"></script>
                                                {{-- Script to Format Date --}}
                                                <script>
                                                    document.addEventListener('DOMContentLoaded', function() {
                                                        // Function to format dates using moment.js
                                                        function formatDate(dateString) {
                                                            return moment(dateString).format('MMMM D, YYYY h:mm A');
                                                        }

                                                        // Format all check-in dates
                                                        document.querySelectorAll('.date').forEach(function(element) {
                                                            const date = element.getAttribute('data-date');
                                                            element.textContent = formatDate(date);
                                                        });

                                                    });
                                                </script>
                                                <td>
                                                    <a href="{{ route('admin.invoice.edit', $invoice->id) }}"
                                                        class="btn btn-primary btn-circle btn-sm" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form action="{{ route('admin.invoice.destroy', $invoice->id) }}"
                                                        method="POST" style="display: inline;" class="deleteForm">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-danger btn-circle btn-sm deleteButton" title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>

                                                    {{-- PayPal --}}
                                                    <a target="#" href="{{ route('admin.payments.paypal', $invoice->id) }}"
                                                        class="btn btn-success btn-circle btn-sm" title="Paypal">
                                                        <i class="fab fa-paypal"></i>
                                                    </a>

                                                    {{-- Pay Cash --}}
                                                    <a href="{{ route('admin.payments.cash', $invoice->id) }}"
                                                        class="btn btn-success btn-circle btn-sm" title="Pay">
                                                        <i class="fas fa-money-bill-wave"></i>
                                                    </a>

                                                    {{-- PayMongo --}}
                                                    <a target="#" href="{{ route('admin.payments.paymongo', $invoice->id) }}"
                                                        class="btn btn-success btn-circle btn-sm" title="Paymongo">
                                                        <i class="fas fa-money-bill-wave"></i>
                                                    </a>

                                                    {{-- Guest Portal Link --}}
                                                    @if($invoice->booking && $invoice->booking->guest_email)
                                                        <button type="button"
                                                                class="btn btn-info btn-circle btn-sm"
                                                                title="Generate Guest Portal Link"
                                                                onclick="generateGuestLink({{ $invoice->id }})">
                                                            <i class="fas fa-link"></i>
                                                        </button>
                                                    @endif
                                                </td>

                                                <!-- QR Codes -->
                                                <td>
                                                    <div class="btn-group-vertical btn-group-sm">
                                                        <a target="_blank"
                                                            href="{{ route('generate.qr', ['type' => 'paypal', 'id' => $invoice->id]) }}"
                                                            class="btn btn-outline-primary btn-sm mb-1">
                                                            <i class="fab fa-paypal"></i> PayPal QR
                                                        </a>
                                                        <a target="_blank"
                                                            href="{{ route('generate.qr', ['type' => 'paymongo', 'id' => $invoice->id]) }}"
                                                            class="btn btn-outline-success btn-sm mb-1">
                                                            <i class="fas fa-mobile-alt"></i> PayMongo QR
                                                        </a>
                                                        <a target="_blank"
                                                            href="{{ route('guest-portal.guest-payment-qr', $invoice->id) }}"
                                                            class="btn btn-outline-info btn-sm mb-1">
                                                            <i class="fas fa-qrcode"></i> Guest Portal
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>

                                <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        // Function to format dates using moment.js
                                        function formatDate(dateString) {
                                            return moment(dateString).format('MMMM D, YYYY h:mm A');
                                        }

                                        // Format all check-in dates
                                        document.querySelectorAll('.checkin-date').forEach(function(element) {
                                            const date = element.getAttribute('data-date');
                                            element.textContent = formatDate(date);
                                        });

                                        // Format all check-out dates
                                        document.querySelectorAll('.checkout-date').forEach(function(element) {
                                            const date = element.getAttribute('data-date');
                                            element.textContent = formatDate(date);
                                        });
                                    });
                                </script>
                            </div>
                        </div>
                    </div>
                    
                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            @include('layout.admin-footer')
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary" href="login.html">Logout</a>
                </div>
            </div>
        </div>
    </div>

    @include('layout.scripts')

    <!-- Guest Portal Link Modal -->
    <div class="modal fade" id="guestLinkModal" tabindex="-1" aria-labelledby="guestLinkModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="guestLinkModalLabel">Guest Portal Access Link</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>Share this link with the guest to allow them to view and pay their invoice:</p>
                    <div class="input-group">
                        <input type="text" class="form-control" id="guestLinkUrl" readonly>
                        <div class="input-group-append">
                            <button class="btn btn-outline-secondary" type="button" onclick="copyGuestLink()">
                                <i class="fas fa-copy"></i> Copy
                            </button>
                        </div>
                    </div>
                    <div class="mt-3">
                        <small class="text-muted">
                            <strong>Guest Email:</strong> <span id="guestEmail"></span><br>
                            <strong>Invoice #:</strong> <span id="invoiceNumber"></span>
                        </small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="copyGuestLink()">
                        <i class="fas fa-copy"></i> Copy Link
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
    function generateGuestLink(invoiceId) {
        fetch(`/admin/invoices/${invoiceId}/guest-link`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('guestLinkUrl').value = data.url;
                    document.getElementById('guestEmail').textContent = data.guest_email;
                    document.getElementById('invoiceNumber').textContent = data.invoice_id;

                    // Show modal
                    $('#guestLinkModal').modal('show');
                } else {
                    alert('Error generating guest link');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error generating guest link');
            });
    }

    function copyGuestLink() {
        const linkInput = document.getElementById('guestLinkUrl');
        linkInput.select();
        linkInput.setSelectionRange(0, 99999); // For mobile devices

        if (navigator.clipboard) {
            navigator.clipboard.writeText(linkInput.value).then(function() {
                showCopySuccess();
            }).catch(function(err) {
                console.error('Could not copy text: ', err);
                fallbackCopy();
            });
        } else {
            fallbackCopy();
        }
    }

    function fallbackCopy() {
        const linkInput = document.getElementById('guestLinkUrl');
        linkInput.select();
        document.execCommand('copy');
        showCopySuccess();
    }

    function showCopySuccess() {
        const button = event.target.closest('button');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i> Copied!';
        button.classList.remove('btn-outline-secondary', 'btn-primary');
        button.classList.add('btn-success');

        setTimeout(() => {
            button.innerHTML = originalText;
            button.classList.remove('btn-success');
            button.classList.add('btn-primary');
        }, 2000);
    }
    </script>

</body>

</html>
