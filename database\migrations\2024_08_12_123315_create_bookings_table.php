<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bookings', function (Blueprint $table) {
            $table->id();
            $table->string('guest_first_name');
            $table->string('guest_last_name');
            $table->string('guest_email')->nullable();
            $table->string('guest_phone')->nullable();
            $table->integer('guest_id')->nullable();//vince added
            $table->foreignId('room_id')->constrained()->onDelete('cascade');
            $table->dateTime('checkin_date');
            $table->dateTime('checkout_date');
            $table->decimal('total_price', 10, 2);
            $table->enum('booking_status', ['pending', 'booked', 'checked_in','checked_out', 'cancelled'])->default('pending');
            $table->string('booked_by')->nullable();
            $table->string('last_updated_by')->nullable();
            $table->string('cancelled_by')->nullable();
            $table->string('checked_in_by')->nullable();
            $table->string('checked_out_by')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();
        });

        DB::table('bookings')->insert([
            [
                'guest_first_name' => 'John',
                'guest_last_name' => 'Doe',
                'guest_email' => '<EMAIL>',
                'guest_phone' => '555-1234',
                'guest_id' => 1,
                'room_id' => 1,
                'checkin_date' => Carbon::now()->subDays(5),
                'checkout_date' => Carbon::now()->subDays(3),
                'total_price' => 500.00,
                'booking_status' => 'checked_out',
                'booked_by' => 'Admin',
                'last_updated_by' => 'Manager',
                'cancelled_by' => null,
                'checked_in_by' => 'Receptionist',
                'checked_out_by' => 'Receptionist',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'guest_first_name' => 'Jane',
                'guest_last_name' => 'Smith',
                'guest_email' => '<EMAIL>',
                'guest_phone' => '555-5678',
                'guest_id' => null,
                'room_id' => 2,
                'checkin_date' => Carbon::now()->subDays(3),
                'checkout_date' => Carbon::now()->addDay(),
                'total_price' => 300.00,
                'booking_status' => 'checked_in',
                'booked_by' => 'Staff',
                'last_updated_by' => 'Manager',
                'cancelled_by' => null,
                'checked_in_by' => 'Receptionist',
                'checked_out_by' => null,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'guest_first_name' => 'Mark',
                'guest_last_name' => 'Johnson',
                'guest_email' => '<EMAIL>',
                'guest_phone' => '555-9012',
                'guest_id' => null,
                'room_id' => 3,
                'checkin_date' => Carbon::now()->addDays(2),
                'checkout_date' => Carbon::now()->addDays(4),
                'total_price' => 400.00,
                'booking_status' => 'booked',
                'booked_by' => 'Admin',
                'last_updated_by' => 'Admin',
                'cancelled_by' => null,
                'checked_in_by' => null,
                'checked_out_by' => null,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'guest_first_name' => 'Emily',
                'guest_last_name' => 'Williams',
                'guest_email' => '<EMAIL>',
                'guest_phone' => '555-3456',
                'guest_id' => null,
                'room_id' => 4,
                'checkin_date' => Carbon::now()->subWeek(),
                'checkout_date' => Carbon::now()->subDays(4),
                'total_price' => 600.00,
                'booking_status' => 'checked_out',
                'booked_by' => 'Manager',
                'last_updated_by' => null,
                'cancelled_by' => null,
                'checked_in_by' => 'Staff',
                'checked_out_by' => 'Staff',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'guest_first_name' => 'Michael',
                'guest_last_name' => 'Brown',
                'guest_email' => '<EMAIL>',
                'guest_phone' => '555-7890',
                'guest_id' => null,
                'room_id' => 5,
                'checkin_date' => Carbon::now()->subDays(10),
                'checkout_date' => Carbon::now()->subDays(8),
                'total_price' => 450.00,
                'booking_status' => 'checked_out',
                'booked_by' => 'Admin',
                'last_updated_by' => 'Manager',
                'cancelled_by' => null,
                'checked_in_by' => 'Receptionist',
                'checked_out_by' => 'Receptionist',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bookings');
    }
};
