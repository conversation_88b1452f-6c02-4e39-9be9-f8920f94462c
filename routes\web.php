<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Route;
use App\Http\Middleware\AdminMiddleware;

Route::get('/', function () {
    return redirect()->route('login');
});

Route::get('/unauthorized', function () {
    return view('unauthorized');
})->name('unauthorized');

// Hotel Registration Routes (Public)
Route::get('/register-hotel', [HotelRegistrationController::class, 'showRegistrationForm'])->name('hotel.register.form');
Route::post('/register-hotel', [HotelRegistrationController::class, 'register'])->name('hotel.register');

//Dashboard with middleware auth and verified - redirect to admin
Route::get('/dashboard', function () {
    return redirect()->route('admin.index');
})->middleware(['auth', 'verified'])->name('dashboard');


//Profile with middleware auth
Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

//Admin Panel
Route::middleware(['auth', AdminMiddleware::class, \App\Http\Middleware\TenantMiddleware::class])->group(function () {
    Route::get('/admin', [PageController::class, 'admin_index'])->name('admin.index');

    //API to fetch chart data for dashboard
    Route::get('/admin/chart-data', [PageController::class, 'fetchChartData'])->name('admin.chart-data');
    Route::post('/admin/fetch-chart-data', [PageController::class, 'fetchChartData'])->name('admin.post.chart-data');
    

    //Admin Panel Logout




    //Admin Bookings
    Route::get('/admin/bookings', [BookingController::class, 'index'])->name('admin.bookings');
    Route::get('/admin/bookings/view/{id}', [BookingController::class, 'showBooking'])->name('admin.Viewbooking');
    Route::get('/admin/bookings/add', [BookingController::class, 'add'])->name('admin.booking.add');
    Route::post('/get-available-rooms', [BookingController::class, 'getAvailableRooms'])->name('get.available.rooms');
    Route::post('/admin/bookings', [BookingController::class, 'store'])->name('admin.booking.store');
    Route::get('/admin/bookings/edit/{id}', [BookingController::class, 'edit'])->name('admin.booking.edit');
    Route::patch('/admin/bookings/{id}', [BookingController::class, 'update'])->name('admin.booking.update');
    Route::delete('/admin/bookings/{id}', [BookingController::class, 'destroy'])->name('admin.booking.destroy');


    //Admin Rooms
    Route::get('/admin/rooms', [RoomController::class, 'index'])->name('admin.rooms');
    Route::get('/admin/rooms/add', [RoomController::class, 'add'])->name('admin.room.add');
    Route::post('/admin/rooms', [RoomController::class, 'store'])->name('admin.room.store');
    Route::get('/admin/rooms/edit/{id}', [RoomController::class, 'edit'])->name('admin.room.edit');
    Route::patch('/admin/rooms/{id}', [RoomController::class, 'update'])->name('admin.room.update');
    Route::delete('/admin/rooms/{id}', [RoomController::class, 'destroy'])->name('admin.room.destroy');


    //Admin RoomTypes
    Route::get('/admin/roomtypes', [RoomTypeController::class, 'index'])->name('admin.roomtypes');
    Route::get('/admin/roomtypes/add', [RoomTypeController::class, 'add'])->name('admin.roomtype.add');
    Route::post('/admin/roomtypes', [RoomTypeController::class, 'store'])->name('admin.roomtype.store');
    Route::get('/admin/roomtypes/edit/{id}', [RoomTypeController::class, 'edit'])->name('admin.roomtype.edit');
    Route::patch('/admin/roomtypes/{id}', [RoomTypeController::class, 'update'])->name('admin.roomtype.update');
    Route::delete('/admin/roomtypes/{id}', [RoomTypeController::class, 'destroy'])->name('admin.roomtype.destroy');

    //Admin Invoices
    Route::get('/admin/invoices', [InvoiceController::class, 'index'])->name('admin.invoices');
    Route::get('/admin/invoices/view/{id}', [InvoiceController::class, 'showInvoice'])->name('admin.Viewinvoice');
    Route::get('/admin/invoices/add/{id}', [InvoiceController::class, 'add'])->name('admin.invoice.add');
    Route::post('/admin/invoices', [InvoiceController::class, 'store'])->name('admin.invoice.store');
    Route::get('/admin/invoices/edit/{id}', [InvoiceController::class, 'edit'])->name('admin.invoice.edit');
    Route::patch('/admin/invoices/{id}', [InvoiceController::class, 'update'])->name('admin.invoice.update');
    Route::delete('/admin/invoices/{id}', [InvoiceController::class, 'destroy'])->name('admin.invoice.destroy');
    Route::get('/admin/invoices/{id}/guest-link', [InvoiceController::class, 'generateGuestLink'])->name('admin.invoice.guest-link');
    Route::get('/admin/posinvoices', [InvoiceController::class, 'posinvoices'])->name('admin.posinvoices');//vince added
    Route::get('/admin/posinvoice/view/{id}', [InvoiceController::class, 'viewPosInv'])->name('admin.posinvoice.view');//vince added
    Route::get('/admin/posinvoice/print/{id}', [InvoiceController::class, 'printInvoice'])->name('admin.posinvoice.print');//vince added

    //Admin Payments
    Route::get('/admin/payments', [PaymentController::class, 'index'])->name('admin.payments');
    Route::get('/admin/payments/add/{invoice_id}', [PaymentController::class, 'add_cash'])->name('admin.payments.cash');
    Route::post('/admin/payments', [PaymentController::class, 'store_cash'])->name('admin.payment.store.cash');
    Route::get('/admin/payments/edit/{id}', [PaymentController::class, 'edit'])->name('admin.payment.edit');

    //Admin Expenses
    Route::get('/admin/expenses', [ExpenseController::class, 'index'])->name('admin.expenses');
    Route::get('/admin/expenses/add', [ExpenseController::class, 'add'])->name('admin.expense.add');
    Route::post('/admin/expenses', [ExpenseController::class, 'store'])->name('admin.expense.store');
    Route::get('/admin/expenses/edit/{id}', [ExpenseController::class, 'edit'])->name('admin.expense.edit');
    Route::patch('/admin/expenses/{id}', [ExpenseController::class, 'update'])->name('admin.expense.update');
    Route::delete('/admin/expenses/{id}', [ExpenseController::class, 'destroy'])->name('admin.expense.destroy');

    //Admin Guests
    Route::get('/admin/guests', [GuestController::class, 'index'])->name('admin.guests');
    Route::get('/admin/guests/add', [GuestController::class, 'add'])->name('admin.guest.add');
    Route::post('/admin/guests', [GuestController::class, 'store'])->name('admin.guest.store');
    Route::get('/admin/guests/edit/{id}', [GuestController::class, 'edit'])->name('admin.guest.edit');
    Route::patch('/admin/guests/{id}', [GuestController::class, 'update'])->name('admin.guest.update');
    Route::delete('/admin/guests/{id}', [GuestController::class, 'destroy'])->name('admin.guest.destroy');
    Route::get('/ajax/admin/guests', [GuestController::class, 'ajaxgetGuests'])->name('admin.ajax.getGuests');

    //Paypal Payments
    Route::get('/admin/payments/paypal/{invoice_id}', [PayPalController::class, 'index'])->name('admin.payments.paypal');
    Route::get('/admin/payments/paypal/create/{amount}', [PayPalController::class, 'create'])->name('admin.payments.create');
    Route::post('/admin/payments/paypal/complete', [PayPalController::class, 'complete'])->name('admin.payments.complete');

    //Paymongo Payments
    Route::get('/admin/payments/paymongo/pay/{invoice_id}', [PayMongoController::class, 'pay'])->name('admin.payments.paymongo');
    Route::post('/admin/payments/paymongo/payfood_order', [PayMongoController::class, 'payfood_order'])->name('admin.payments.paymongo.payfood_order');
    Route::get('/admin/payments/paymongo/payfood_order_success', [PayMongoController::class, 'payfood_order_success'])->name('admin.payments.paymongo.get.payfood_order_success');
    Route::get('/admin/payments/paymongo/success/{invoice_id}', [PayMongoController::class, 'success'])->name('admin.payments.paymongo.get.success');
    Route::get('/admin/payments/paymongo/cancel', [PayMongoController::class, 'cancel'])->name('admin.payments.paymongo.get.cancel');

    //Admin Remittances
    Route::get('/admin/remittances', [RemittanceController::class, 'index'])->name('admin.remittances');
    Route::get('/admin/remittances/add/{payment_id}', [RemittanceController::class, 'add'])->name('admin.remittances.add');
    Route::post('/admin/remittances', [RemittanceController::class, 'store'])->name('admin.remittances.store');
    Route::get('/admin/remittances/confirm/{remittance_id}', [RemittanceController::class, 'confirm'])->name('admin.remittances.confirm');
    Route::post('/admin/remittances/transfer', [RemittanceController::class, 'transfer'])->name('admin.remittances.transfer');
    Route::post('/admin/remittances/owner', [RemittanceController::class, 'owner'])->name('admin.remittances.owner');
    Route::get('/admin/remittances/bank', [RemittanceController::class, 'bank'])->name('admin.remittances.bank');
    Route::get('/admin/remittances/bank/{remittance_id}', [RemittanceController::class, 'bank_single'])->name('admin.remittances.bank.single');
    Route::post('/admin/remittances/bank', [RemittanceController::class, 'bank'])->name('admin.remittances.bank.post');
    Route::post('/admin/remittances/documents/upload', [RemittanceController::class, 'remittance_documents_upload'])->name('remittance.documents.upload');
    Route::post('/admin/remittances/documents/upload/single', [RemittanceController::class, 'remittance_documents_upload_single'])->name('remittance.documents.upload.single');

    Route::get('/admin/remittances/edit/{id}', [RemittanceController::class, 'edit'])->name('admin.remittances.edit');
    Route::patch('/admin/remittances/{id}', [RemittanceController::class, 'update'])->name('admin.remittances.update');
    Route::delete('/admin/remittances/{id}', [RemittanceController::class, 'destroy'])->name('admin.remittances.destroy');

    //Admin Events
    Route::get('/admin/events', [EventController::class, 'index'])->name('admin.events');
    Route::get('/admin/events/add', [EventController::class, 'add'])->name('admin.event.add');
    Route::post('/admin/events', [EventController::class, 'store'])->name('admin.event.store');
    Route::get('/admin/events/edit/{id}', [EventController::class, 'edit'])->name('admin.event.edit');
    Route::post('/admin/events/edit/{id}', [EventController::class, 'update'])->name('admin.post.event.edit');//vince added
    Route::patch('/admin/events/{id}', [EventController::class, 'update'])->name('admin.event.update');
    Route::delete('/admin/events/{id}', [EventController::class, 'destroy'])->name('admin.event.destroy');
    Route::post('/admin/events/complete/{id}', [EventController::class, 'completeEvent'])->name('admin.completeEvent');

    //Admin Menu
    Route::get('/admin/menus', [MenuController::class, 'index'])->name('admin.menus');
    Route::get('/admin/menus/add', [MenuController::class, 'add'])->name('admin.menu.add');
    Route::post('/admin/menus', [MenuController::class, 'store'])->name('admin.menu.store');
    Route::get('/admin/menus/edit/{id}', [MenuController::class, 'edit'])->name('admin.menu.edit');
    Route::patch('/admin/menus/{id}', [MenuController::class, 'update'])->name('admin.menu.update');
    Route::delete('/admin/menus/{id}', [MenuController::class, 'destroy'])->name('admin.menu.destroy');

    //Admin Ingredients
    Route::get('/admin/ingredients', [IngredientController::class, 'index'])->name('admin.ingredients');
    Route::get('/admin/ingredients/add', [IngredientController::class, 'add'])->name('admin.ingredient.add');
    Route::post('/admin/ingredients', [IngredientController::class, 'store'])->name('admin.ingredient.store');
    Route::get('/admin/ingredients/edit/{id}', [IngredientController::class, 'edit'])->name('admin.ingredient.edit');
    Route::patch('/admin/ingredients/{id}', [IngredientController::class, 'update'])->name('admin.ingredient.update');
    Route::delete('/admin/ingredients/{id}', [IngredientController::class, 'destroy'])->name('admin.ingredient.destroy');

    //Admin Kitchen
    Route::get('/admin/kitchen/view', [KitchenController::class, 'kitchenView'])->name('admin.kitchenView');
    Route::get('/admin/kitchen/search', [KitchenController::class, 'search'])->name('kitchen.search');

    Route::post('/grocery/update-stock', [GroceryController::class, 'updateStock']);
    Route::post('/kitchen/confirm', [KitchenController::class, 'confirmKitchen']);
    Route::post('/grocery/update-purchase-status', [GroceryController::class, 'updatePurchaseStatus'])->name('grocery.update-purchase-status');

    //Admin Grocery
    Route::get('/admin/grocery/view', [GroceryController::class, 'groceryView'])->name('admin.groceryView');
    Route::get('/admin/grocery/search', [GroceryController::class, 'search'])->name('grocery.search');

    //Admin Users/Staff
    Route::get('/admin/users', [UserController::class, 'index'])->name('admin.users');
    Route::get('/admin/users/add', [UserController::class, 'add'])->name('admin.user.add');
    Route::post('/admin/users', [UserController::class, 'store'])->name('admin.user.store');
    Route::get('/admin/users/edit/{id}', [UserController::class, 'edit'])->name('admin.user.edit');
    Route::patch('/admin/users/{id}', [UserController::class, 'update'])->name('admin.user.update');
    Route::delete('/admin/users/{id}', [UserController::class, 'destroy'])->name('admin.user.destroy');

    Route::get('/admin/staff', [UserController::class, 'staff'])->name('admin.staff');

    //Admin Orders
    Route::get('/admin/orders', [OrderController::class, 'index'])->name('admin.orders');
    Route::get('/admin/orders/view/{id}', [OrderController::class, 'showOrder'])->name('admin.Vieworder');
    Route::get('/admin/orders/add', [OrderController::class, 'add'])->name('admin.order.add');
    Route::post('/admin/orders', [OrderController::class, 'store'])->name('admin.order.store');
    Route::post('/get-available-rooms/restaurant', [BookingController::class, 'getAvailableRoomsforRestaurant'])->name('get.available.rooms.forRestaurant');
    Route::get('/admin/orders/edit/{id}', [OrderController::class, 'edit'])->name('admin.order.edit');
    Route::patch('/admin/orders/{id}', [OrderController::class, 'update'])->name('admin.order.update');
    Route::post('/admin/orders/{id}', [OrderController::class, 'update'])->name('admin.post.order.update');//vince added
    Route::delete('/admin/orders/{id}', [OrderController::class, 'destroy'])->name('admin.order.destroy');


    //Admin Activity Logs
    Route::get('/admin/activity-logs', [PageController::class, 'activity_logs'])->name('admin.activity-logs');


    //QR Code
    Route::get('/generate-qr/{type}/{id}', [QrCodeController::class, 'generateQrCode'])->name('generate.qr');


    // Settings Routes
    Route::prefix('admin/settings')->name('admin.settings.')->group(function () {
        Route::get('general', [SettingsController::class, 'general'])->name('general');
        Route::get('payment', [SettingsController::class, 'payment'])->name('payment');
        Route::post('update', [SettingsController::class, 'update'])->name('update');
        // Add more routes as needed
    });

    // Hotel Management Routes (Super Admin only)
    Route::prefix('admin/hotels')->name('admin.hotels.')->group(function () {
        Route::get('/', [HotelController::class, 'index'])->name('index');
        Route::get('/create', [HotelController::class, 'create'])->name('create');
        Route::post('/', [HotelController::class, 'store'])->name('store');
        Route::get('/{hotel}', [HotelController::class, 'show'])->name('show');
        Route::get('/{hotel}/edit', [HotelController::class, 'edit'])->name('edit');
        Route::patch('/{hotel}', [HotelController::class, 'update'])->name('update');
    });

    // Hotel Switching
    Route::post('/admin/switch-hotel', [HotelController::class, 'switch'])->name('admin.switch-hotel');
    Route::get('/admin/current-hotel', [HotelController::class, 'current'])->name('admin.current-hotel');

    // Subscription Management Routes
    Route::prefix('admin/subscription')->name('admin.subscription.')->group(function () {
        Route::get('/plans', [SubscriptionController::class, 'plans'])->name('plans');
        Route::get('/current', [SubscriptionController::class, 'current'])->name('current');
        Route::get('/upgrade/{plan}', [SubscriptionController::class, 'upgrade'])->name('upgrade');
        Route::post('/upgrade/{plan}', [SubscriptionController::class, 'processUpgrade'])->name('process-upgrade');
        Route::post('/cancel', [SubscriptionController::class, 'cancel'])->name('cancel');
        Route::post('/resume', [SubscriptionController::class, 'resume'])->name('resume');
        Route::get('/billing', [SubscriptionController::class, 'billing'])->name('billing');
        Route::get('/invoice/{invoice}/download', [SubscriptionController::class, 'downloadInvoice'])->name('invoice.download');
    });

    // Branding Management Routes
    Route::prefix('admin/branding')->name('admin.branding.')->group(function () {
        Route::get('/', [BrandingController::class, 'index'])->name('index');
        Route::post('/update', [BrandingController::class, 'update'])->name('update');
        Route::post('/reset', [BrandingController::class, 'reset'])->name('reset');
        Route::post('/preview', [BrandingController::class, 'preview'])->name('preview');
        Route::get('/suggest-subdomains', [BrandingController::class, 'suggestSubdomains'])->name('suggest-subdomains');
    });
});

require __DIR__ . '/auth.php';

// Test route for debugging
Route::get('/portal-test', function () {
    return 'Guest portal routing is working!';
});

// Guest Portal Routes (Public - no authentication required)
Route::prefix('portal')->name('guest-portal.')->group(function () {
    // Browse all hotels (Trivago-style)
    Route::get('/', [\App\Http\Controllers\GuestPortalController::class, 'browseHotels'])->name('browse');

    // Hotel landing page
    Route::get('/{hotel:slug}', [\App\Http\Controllers\GuestPortalController::class, 'hotelPortal'])->name('hotel');

    // Guest access form
    Route::get('/{hotel:slug}/access', [\App\Http\Controllers\GuestPortalController::class, 'showAccessForm'])->name('access');
    Route::post('/{hotel:slug}/access', [\App\Http\Controllers\GuestPortalController::class, 'authenticate'])->name('authenticate');

    // Guest dashboard and booking
    Route::get('/{hotel:slug}/dashboard', [\App\Http\Controllers\GuestPortalController::class, 'dashboard'])->name('dashboard');
    Route::get('/{hotel:slug}/book', [\App\Http\Controllers\GuestPortalController::class, 'showBookingForm'])->name('book');
    Route::post('/{hotel:slug}/available-rooms', [\App\Http\Controllers\GuestPortalController::class, 'getAvailableRooms'])->name('available-rooms');
    Route::post('/{hotel:slug}/book', [\App\Http\Controllers\GuestPortalController::class, 'storeBooking'])->name('store-booking');
    Route::get('/{hotel:slug}/booking/{booking}/confirmation', [\App\Http\Controllers\GuestPortalController::class, 'bookingConfirmation'])->name('booking-confirmation');

    // Invoice viewing
    Route::get('/{hotel:slug}/invoice/{invoice}', [\App\Http\Controllers\GuestPortalController::class, 'showInvoice'])->name('invoice');

    // Payment routes
    Route::get('/{hotel:slug}/invoice/{invoice}/pay', [\App\Http\Controllers\GuestPortalPaymentController::class, 'showPayment'])->name('payment');
    Route::get('/{hotel:slug}/invoice/{invoice}/pay/paypal', [\App\Http\Controllers\GuestPortalPaymentController::class, 'paypalPayment'])->name('payment-paypal');
    Route::get('/{hotel:slug}/invoice/{invoice}/pay/paymongo', [\App\Http\Controllers\GuestPortalPaymentController::class, 'paymongoPayment'])->name('payment-paymongo');
    Route::get('/{hotel:slug}/invoice/{invoice}/payment-success', [\App\Http\Controllers\GuestPortalPaymentController::class, 'paymongoSuccess'])->name('payment-success');
    Route::get('/{hotel:slug}/invoice/{invoice}/payment-cancel', [\App\Http\Controllers\GuestPortalPaymentController::class, 'paymongoCancel'])->name('payment-cancel');
    Route::get('/{hotel:slug}/invoice/{invoice}/payment-complete', [\App\Http\Controllers\GuestPortalPaymentController::class, 'paymentComplete'])->name('payment-complete');

    // QR Code generation for payments
    Route::get('/qr/payment/{invoice}', [\App\Http\Controllers\GuestPortalPaymentController::class, 'generatePaymentQR'])->name('payment-qr');
    Route::get('/qr/guest-payment/{invoice}', [\App\Http\Controllers\QrCodeController::class, 'generateGuestPaymentQR'])->name('guest-payment-qr');
    Route::get('/qr/guest-invoice/{invoice}', [\App\Http\Controllers\QrCodeController::class, 'generateGuestInvoiceQR'])->name('guest-invoice-qr');
    Route::get('/qr/hotel-portal/{hotel}', [\App\Http\Controllers\QrCodeController::class, 'generateHotelPortalQR'])->name('hotel-portal-qr');

    // Logout
    Route::post('/{hotel:slug}/logout', [\App\Http\Controllers\GuestPortalController::class, 'logout'])->name('logout');
});
