@extends('guest-portal.layout')

@section('title', 'Existing Guest Access')

@section('header')
    <div class="text-center">
        <h1 class="display-5 fw-bold mb-3">Existing Guest Access</h1>
        <p class="lead">Already have a booking? Access your reservation details and invoices</p>
    </div>
@endsection

@section('content')
<div class="row justify-content-center">
    <div class="col-lg-8">
        <!-- Important Notice -->
        <div class="alert alert-info mb-4">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h5 class="alert-heading mb-2">
                        <i class="fas fa-info-circle me-2"></i>Looking to make a new booking?
                    </h5>
                    <p class="mb-0">This page is for guests who already have existing reservations. If you want to book a new room, use our booking system instead.</p>
                </div>
                <div class="col-md-4 text-md-end mt-3 mt-md-0">
                    <a href="{{ route('guest-portal.book', $hotel->slug) }}" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>Book New Room
                    </a>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Access Form -->
            <div class="col-lg-7">
                <div class="card">
                    <div class="card-header bg-hotel-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-sign-in-alt me-2"></i>Access Your Existing Booking
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ route('guest-portal.authenticate', $hotel->slug) }}">
                            @csrf
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>Email Address
                                </label>
                                <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                       id="email" name="email" value="{{ old('email') }}" 
                                       placeholder="Enter the email used for your booking" required>
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="reference_type" class="form-label">
                                    <i class="fas fa-tag me-1"></i>What do you have?
                                </label>
                                <select class="form-select @error('reference_type') is-invalid @enderror" 
                                        id="reference_type" name="reference_type" required>
                                    <option value="">Choose what information you have</option>
                                    <option value="booking" {{ old('reference_type') == 'booking' ? 'selected' : '' }}>
                                        Booking/Confirmation Number
                                    </option>
                                    <option value="invoice" {{ old('reference_type') == 'invoice' ? 'selected' : '' }}>
                                        Invoice Number
                                    </option>
                                </select>
                                @error('reference_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-4">
                                <label for="reference" class="form-label">
                                    <i class="fas fa-hashtag me-1"></i>Reference Number
                                </label>
                                <input type="text" class="form-control @error('reference') is-invalid @enderror" 
                                       id="reference" name="reference" value="{{ old('reference') }}" 
                                       placeholder="Enter your reference number" required>
                                @error('reference')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text" id="referenceHelp">
                                    Select the type above to see specific instructions
                                </div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-hotel-primary btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>Access My Booking
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Help Section -->
            <div class="col-lg-5">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-question-circle me-2"></i>What can I access?
                        </h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                View your booking details
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                Check reservation status
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                View and pay invoices
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                Download receipts
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-lightbulb me-2"></i>Quick Guide
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <strong>Booking Number:</strong>
                            <p class="small text-muted mb-0">
                                Usually starts with letters like "BK" followed by numbers (e.g., BK123456)
                            </p>
                        </div>
                        <div class="mb-3">
                            <strong>Invoice Number:</strong>
                            <p class="small text-muted mb-0">
                                Typically just numbers (e.g., 12345)
                            </p>
                        </div>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-headset me-2"></i>Need Help?
                        </h6>
                    </div>
                    <div class="card-body text-center">
                        <p class="small text-muted mb-3">Can't find your reference information?</p>
                        <div class="d-grid gap-2">
                            @if($hotel->phone)
                                <a href="tel:{{ $hotel->phone }}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-phone me-1"></i>Call: {{ $hotel->phone }}
                                </a>
                            @endif
                            @if($hotel->email)
                                <a href="mailto:{{ $hotel->email }}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-envelope me-1"></i>Email: {{ $hotel->email }}
                                </a>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <div class="text-center mt-4">
            <a href="{{ route('guest-portal.hotel', $hotel->slug) }}" class="btn btn-outline-secondary me-2">
                <i class="fas fa-arrow-left me-2"></i>Back to Hotel
            </a>
            <a href="{{ route('guest-portal.browse') }}" class="btn btn-outline-primary">
                <i class="fas fa-search me-2"></i>Browse All Hotels
            </a>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.getElementById('reference_type').addEventListener('change', function() {
    const referenceHelp = document.getElementById('referenceHelp');
    const referenceInput = document.getElementById('reference');
    
    if (this.value === 'booking') {
        referenceHelp.innerHTML = '<i class="fas fa-info-circle text-primary me-1"></i>Enter your booking confirmation number (e.g., BK123456)';
        referenceInput.placeholder = 'e.g., BK123456';
    } else if (this.value === 'invoice') {
        referenceHelp.innerHTML = '<i class="fas fa-info-circle text-primary me-1"></i>Enter your invoice number (e.g., 12345)';
        referenceInput.placeholder = 'e.g., 12345';
    } else {
        referenceHelp.innerHTML = 'Select the type above to see specific instructions';
        referenceInput.placeholder = 'Enter your reference number';
    }
});
</script>
@endpush
