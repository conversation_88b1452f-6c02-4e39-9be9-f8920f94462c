<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create hotel_user pivot table for many-to-many relationship
        Schema::create('hotel_user', function (Blueprint $table) {
            $table->id();
            $table->foreignId('hotel_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('role')->default('user'); // Role specific to this hotel
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->unique(['hotel_id', 'user_id']);
        });

        // Add hotel_id to existing tables
        $tables = [
            'rooms',
            'room_types', 
            'bookings',
            'guests',
            'invoices',
            'payments',
            'services',
            'service_requests',
            'remittances',
            'events',
            'menus',
            'ingredients',
            'orders',
            'expenses',
            'settings'
        ];

        foreach ($tables as $table) {
            if (Schema::hasTable($table)) {
                Schema::table($table, function (Blueprint $blueprint) {
                    $blueprint->foreignId('hotel_id')->default(1)->constrained()->onDelete('cascade');
                });
            }
        }

        // Assign existing users to the default hotel
        $users = DB::table('users')->get();
        foreach ($users as $user) {
            DB::table('hotel_user')->insert([
                'hotel_id' => 1,
                'user_id' => $user->id,
                'role' => $user->role ?? 'user',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $tables = [
            'settings',
            'expenses', 
            'orders',
            'ingredients',
            'menus',
            'events',
            'remittances',
            'service_requests',
            'services',
            'payments',
            'invoices',
            'guests',
            'bookings',
            'room_types',
            'rooms'
        ];

        foreach ($tables as $table) {
            if (Schema::hasTable($table) && Schema::hasColumn($table, 'hotel_id')) {
                Schema::table($table, function (Blueprint $blueprint) {
                    $blueprint->dropForeign(['hotel_id']);
                    $blueprint->dropColumn('hotel_id');
                });
            }
        }

        Schema::dropIfExists('hotel_user');
    }
};
