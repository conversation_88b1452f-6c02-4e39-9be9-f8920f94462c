# Guest Portal Implementation

## Overview
This implementation adds a guest portal feature to the hotel management system, allowing guests to access their bookings and pay invoices on their own devices.

## Files Created/Modified

### New Controllers
- `app/Http/Controllers/GuestPortalController.php` - Main guest portal functionality
- `app/Http/Controllers/GuestPortalPaymentController.php` - Payment processing for guests

### Enhanced Controllers
- `app/Http/Controllers/QrCodeController.php` - Added guest portal QR code generation
- `app/Http/Controllers/InvoiceController.php` - Added guest link generation

### New Views
- `resources/views/guest-portal/layout.blade.php` - Base layout with hotel branding
- `resources/views/guest-portal/hotel-landing.blade.php` - Hotel portal landing page
- `resources/views/guest-portal/access-form.blade.php` - Guest authentication form
- `resources/views/guest-portal/dashboard.blade.php` - Guest dashboard
- `resources/views/guest-portal/payment.blade.php` - Payment selection page

### Modified Views
- `resources/views/admin/invoices/view-invoices.blade.php` - Added guest portal QR codes and link generation

### Routes
- Added guest portal routes in `routes/web.php`
- Public routes for hotel access and payments
- Secure routes for authenticated guest sessions

## Key Features Implemented

### 1. Guest Authentication
- **Session-based authentication** using email + booking/invoice reference
- **24-hour session expiry** for security
- **Hotel-specific access** - guests can only see their hotel's data

### 2. Payment Integration
- **PayMongo integration** for digital wallets (GCash, PayMaya, GrabPay)
- **PayPal integration** for international payments
- **Secure payment processing** with existing gateway configurations

### 3. QR Code System
- **Enhanced QR code generation** for guest portal access
- **Multiple QR code types**: payment, invoice, hotel portal
- **Admin integration** for easy QR code generation

### 4. Hotel Branding
- **Dynamic branding** using hotel's brand colors and logo
- **Responsive design** that works on all devices
- **Consistent styling** with hotel's visual identity

### 5. Admin Integration
- **Guest link generation** from admin panel
- **QR code options** in invoice management
- **Seamless workflow** for hotel staff

## Security Considerations

### Authentication
- No permanent guest accounts created
- Session-based access with expiration
- Email + reference verification required

### Data Protection
- Hotel data isolation enforced
- Guest can only access their own bookings/invoices
- Secure payment processing through established gateways

### Session Management
- 24-hour session expiry
- Secure session tokens
- Automatic logout on expiry

## Usage Instructions

### For Hotel Staff
1. **Generate Guest Links**: Click the link button in invoice management
2. **Create QR Codes**: Use QR code options in admin panel
3. **Share Access**: Provide guests with email + reference + link/QR code

### For Guests
1. **Access Portal**: Scan QR code or use provided link
2. **Authenticate**: Enter email and booking/invoice reference
3. **View Information**: See bookings, invoices, and payment history
4. **Make Payments**: Choose payment method and complete transaction

## Technical Architecture

### Authentication Flow
1. Guest enters email + reference
2. System validates against booking/invoice records
3. Session created with hotel context
4. Guest redirected to dashboard or specific page

### Payment Flow
1. Guest selects payment method
2. System creates payment session with gateway
3. Guest completes payment on gateway
4. System receives confirmation and updates records
5. Guest sees confirmation page

### QR Code Flow
1. Admin generates QR code for invoice
2. QR code contains direct link to payment page
3. Guest scans QR code on their device
4. Direct access to payment without additional authentication

## Configuration

### Environment Variables
Ensure these are configured for payment processing:
- `PAYMONGO_SECRET_KEY` - PayMongo API key
- `PAYPAL_CLIENT_ID` - PayPal client ID
- `PAYPAL_CLIENT_SECRET` - PayPal client secret

### Hotel Settings
- Hotel branding (colors, logos) configured in admin panel
- Payment gateway settings in admin settings

## Testing

### Test Scenarios
1. **Guest Access**: Test with valid email + booking reference
2. **Payment Processing**: Test with different payment methods
3. **QR Code Generation**: Verify QR codes work on mobile devices
4. **Hotel Branding**: Check branding applies correctly
5. **Session Management**: Verify session expiry works

### Test Data Requirements
- Hotel with configured branding
- Booking with guest email
- Invoice associated with booking
- Payment gateway test credentials

## Deployment Notes

### Database Changes
- No new tables required
- Uses existing booking, invoice, and hotel tables
- Leverages existing payment processing

### Dependencies
- SimpleSoftwareIO/QrCode package (already installed)
- Bootstrap 5 for responsive design
- Existing payment gateway integrations

### Performance Considerations
- Session-based authentication is lightweight
- QR code generation is on-demand
- Payment processing uses existing optimized flows

## Future Enhancements

### Planned Features
- Email notifications for booking confirmations
- SMS integration for access codes
- Guest profile management with optional account creation
- Advanced reporting for guest portal usage
- Multi-language support

### Possible Improvements
- Progressive Web App (PWA) capabilities
- Push notifications for payment confirmations
- Integration with hotel loyalty programs
- Advanced analytics for guest behavior
