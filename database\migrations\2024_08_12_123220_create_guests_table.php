<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('guests', function (Blueprint $table) {
            $table->id();
            $table->string('first_name');
            $table->string('last_name');
            $table->string('email')->nullable();
            $table->string('phone');
            $table->string('address')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();
        });

         // Add dummy data
         DB::table('guests')->insert([
            [
                'first_name' => 'John',
                'last_name' => 'Doe',
                'email' => '<EMAIL>',
                'phone' => '555-1234',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'first_name' => '<PERSON>',
                'last_name' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone' => '555-5678',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'first_name' => 'Emily',
                'last_name' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone' => '555-8765',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
        
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('guests');
    }
};
