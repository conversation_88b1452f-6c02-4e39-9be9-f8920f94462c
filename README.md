# How to setup in Local Development? #
1. Clone Repository (Development Branch)
2. Open project folder in VScode or Editor of your choice
3. In terminal run "composer update" to download all necessary packages and libraries (Make sure you have PHP and composer installed)
4. Create new .env file (You can use .env.example for reference)
5. Make sure your .env has all the necessary configurations (database, google, etc)
6. In terminal run "php artisan migrate:fresh" then "php artisan serve"

# hotel_management #
 
Note in Live Production set all .env

Note When in Development mode, Online payments fail to register in local DB if not logged in because it needs session variables

Config Clear fixes .env variables not being read

### User Roles ###
1.Super (Owner)
2.Admin
3.Manager
4.Staff
5.User