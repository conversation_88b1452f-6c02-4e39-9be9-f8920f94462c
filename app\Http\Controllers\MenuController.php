<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Ingredient;
use App\Models\Menu;
use App\Models\Menu_Ingredient;

class MenuController extends Controller
{
    public function index()
    {
        $menus = Menu::all();
        return view('admin.menu.menu-view', [
            'menu_list' => $menus
        ]);
    }

    public function add()
    {
        $ingredients = Ingredient::orderBy('ingredient_name', 'asc')->get();//vince orig Ingredient::all();
        return view('admin.menu.menu-add', [
            'ingredient_list' => $ingredients
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'how_to_cook' => 'nullable|string',
            'price' => 'required|numeric',
            'cook_time' => 'nullable|string|max:255',
            'menu_ingredients' => 'required|array',
            'menu_ingredients.*.menu_id' => 'required|exists:ingredients,id',
            'menu_ingredients.*.quantity' => 'required|numeric',
            'menu_ingredients.*.unit' => 'required|string|max:50',
        ]);

        $menu = new Menu();
        $menu->fill($validated);

        // Save the new Menu to the database
        $menu->save();


        foreach ($validated['menu_ingredients'] as $item) {
            $menuIngredient = new Menu_Ingredient();
            $menuIngredient->menu_id = $menu->id;
            $menuIngredient->ingredient_id = $item['menu_id'];
            $menuIngredient->quantity = $item['quantity'];
            $menuIngredient->unit = $item['unit'];
            $menuIngredient->save();
        }

        //Log activity
        log_activity("Menu added: " . $menu->name, 'Menu controller');

        return redirect()->route('admin.menus')->with('success', 'Menu item created successfully.');
    }

    public function edit($id)
    {
        $menu = Menu::with('ingredients')->findOrFail($id);
        $ingredients = Ingredient::orderBy('ingredient_name', 'asc')->get();//vince orig Ingredient::all();
        return view('admin.menu.menu-edit', [
            'menu' => $menu,
            'ingredient_list' => $ingredients
        ]);
    }
    public function update(Request $request, $id)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'how_to_cook' => 'nullable|string',
            'price' => 'required|numeric',
            'cook_time' => 'nullable|string|max:255',
            'menu_ingredients' => 'required|array',
            'menu_ingredients.*.menu_id' => 'required|exists:ingredients,id',
            'menu_ingredients.*.quantity' => 'required|numeric',
            'menu_ingredients.*.unit' => 'required|string|max:50',
        ]);

        $menu = Menu::findOrFail($id);
        $menu->name = $validated['name'];
        $menu->description = $validated['description'];
        $menu->how_to_cook = $validated['how_to_cook'];
        $menu->price = $validated['price'];
        $menu->cook_time = $validated['cook_time'];
        $menu->save();


        // Delete old ingredients
        Menu_Ingredient::where('menu_id', $menu->id)->delete();

        // Insert new ingredients
        foreach ($validated['menu_ingredients'] as $item) {
            $menuIngredient = new Menu_Ingredient();
            $menuIngredient->menu_id = $menu->id;
            $menuIngredient->ingredient_id = $item['menu_id'];
            $menuIngredient->quantity = $item['quantity'];
            $menuIngredient->unit = $item['unit'];
            $menuIngredient->save();
        }

        //Log activity
        log_activity("Menu updated: " . $menu->name, 'Menu controller');

        return redirect()->route('admin.menus')->with('success', 'Menu item updated successfully.');
    }

    public function destroy($id)
    {
        $menu = Menu::findOrFail($id);
        $menu->delete();
        return redirect()->route('admin.menus')->with('success', 'Menu item deleted successfully.');
    }
}
