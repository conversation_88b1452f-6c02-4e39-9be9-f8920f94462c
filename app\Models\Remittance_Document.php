<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\BelongsToHotel;

class Remittance_Document extends Model
{
    use HasFactory, BelongsToHotel;

    protected $table = 'remittance_documents';
    protected $fillable = [
        'hotel_id',
        'remittance_id',
        'file_name',
        'file_type',
        'file_size',
        'file_path'
    ];

    public function remittance()
    {
        return $this->belongsTo(Remittance::class, 'remittance_id');
    }

    public function payment()
    {
        return $this->belongsTo(Payment::class, 'payment_id');
    }

    
}
