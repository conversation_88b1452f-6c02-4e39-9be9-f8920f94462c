<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->string('customer_name')->nullable();
            $table->string('table_name')->nullable();//vince orig order_number
            $table->string('order_status')->default('pending');
            $table->boolean('room_charge')->default(false);
            $table->decimal('total_price', 10, 2)->default(0);
            $table->foreignId('invoice_id')->nullable()->constrained()->onDelete('set null');//vince added
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
