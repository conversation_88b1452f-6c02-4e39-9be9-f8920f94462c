<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', 'Guest Portal') - {{ $hotel->name ?? 'Hotel' }}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        :root {
            --hotel-primary: {{ $hotel->brand_color ?? '#007bff' }};
            --hotel-primary-dark: {{ $hotel->brand_color ? 'color-mix(in srgb, ' . $hotel->brand_color . ' 80%, black)' : '#0056b3' }};
        }
        
        .navbar-brand img {
            max-height: 40px;
        }
        
        .btn-hotel-primary {
            background-color: var(--hotel-primary);
            border-color: var(--hotel-primary);
            color: white;
        }
        
        .btn-hotel-primary:hover {
            background-color: var(--hotel-primary-dark);
            border-color: var(--hotel-primary-dark);
            color: white;
        }
        
        .text-hotel-primary {
            color: var(--hotel-primary) !important;
        }
        
        .bg-hotel-primary {
            background-color: var(--hotel-primary) !important;
        }
        
        .border-hotel-primary {
            border-color: var(--hotel-primary) !important;
        }
        
        .guest-portal-header {
            background: linear-gradient(135deg, var(--hotel-primary), var(--hotel-primary-dark));
            color: white;
            padding: 2rem 0;
        }
        
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border-radius: 0.5rem;
        }
        
        .card-header {
            background-color: var(--hotel-primary);
            color: white;
            border-radius: 0.5rem 0.5rem 0 0 !important;
        }
        
        .footer {
            background-color: #f8f9fa;
            padding: 2rem 0;
            margin-top: 3rem;
        }
        
        @media (max-width: 768px) {
            .guest-portal-header {
                padding: 1rem 0;
            }
        }
    </style>
    
    @stack('styles')
</head>
<body>
    {{-- Icon to return to /portal --}}
                <a href="{{ route('guest-portal.browse') }}" >
                    <i class="fas fa-house me-2"></i>Home
                </a>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="{{ route('guest-portal.hotel', $hotel->slug ?? '') }}">
                @if($hotel->brand_logo ?? $hotel->logo)
                    <img src="{{ asset('storage/' . ($hotel->brand_logo ?? $hotel->logo)) }}" alt="{{ $hotel->name }}" class="me-2">
                @endif
                <span class="fw-bold text-hotel-primary">{{ $hotel->name ?? 'Hotel' }}</span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    @if(session('guest_portal_token'))
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('guest-portal.dashboard', $hotel->slug) }}">
                                <i class="fas fa-tachometer-alt me-1"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('guest-portal.book', $hotel->slug) }}">
                                <i class="fas fa-bed me-1"></i> Book Room
                            </a>
                        </li>
                        <li class="nav-item">
                            <form method="POST" action="{{ route('guest-portal.logout', $hotel->slug) }}" class="d-inline">
                                @csrf
                                <button type="submit" class="nav-link btn btn-link p-0 border-0">
                                    <i class="fas fa-sign-out-alt me-1"></i> Logout
                                </button>
                            </form>
                        </li>
                    @else
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('guest-portal.access', $hotel->slug) }}">
                                <i class="fas fa-sign-in-alt me-1"></i> Guest Access
                            </a>
                        </li>
                    @endif
                </ul>
            </div>
        </div>
    </nav>

    <!-- Header -->
    @hasSection('header')
        <div class="guest-portal-header">
            <div class="container">
                @yield('header')
            </div>
        </div>
    @endif

    <!-- Main Content -->
    <main class="container my-4">
        <!-- Alerts -->
        @if(session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        @endif

        @if(session('error'))
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        @endif

        @if(session('info'))
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <i class="fas fa-info-circle me-2"></i>{{ session('info') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        @endif

        @if($errors->any())
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <ul class="mb-0">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        @endif

        @yield('content')
    </main>

    <!-- Footer -->
    <footer class="footer mt-auto">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5 class="text-hotel-primary">{{ $hotel->name ?? 'Hotel' }}</h5>
                    @if($hotel->address ?? false)
                        <p class="text-muted mb-1">
                            <i class="fas fa-map-marker-alt me-2"></i>{{ $hotel->address }}
                        </p>
                    @endif
                    @if($hotel->phone ?? false)
                        <p class="text-muted mb-1">
                            <i class="fas fa-phone me-2"></i>{{ $hotel->phone }}
                        </p>
                    @endif
                    @if($hotel->email ?? false)
                        <p class="text-muted mb-1">
                            <i class="fas fa-envelope me-2"></i>{{ $hotel->email }}
                        </p>
                    @endif
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted mb-0">
                        &copy; {{ date('Y') }} {{ $hotel->name ?? 'Hotel' }}. All rights reserved.
                    </p>
                    <p class="text-muted small">
                        Powered by Hotel Management System
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    @stack('scripts')
</body>
</html>
