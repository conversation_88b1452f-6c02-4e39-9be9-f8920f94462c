<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\DB;

class User extends Authenticatable
{
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'user_type',
        'role',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * Get the hotels that the user belongs to.
     */
    public function hotels()
    {
        return $this->belongsToMany(Hotel::class, 'hotel_user')
                    ->withPivot('role', 'is_active')
                    ->withTimestamps();
    }

    /**
     * Get the user's role for a specific hotel.
     */
    public function getRoleForHotel($hotelId)
    {
        $hotel = $this->hotels()->where('hotel_id', $hotelId)->first();
        return $hotel ? $hotel->pivot->role : null;
    }

    /**
     * Check if user has access to a specific hotel.
     */
    public function hasAccessToHotel($hotelId)
    {
        return $this->hotels()->where('hotel_id', $hotelId)->where('is_active', true)->exists();
    }

    /**
     * Check if user is super admin (can access all hotels).
     */
    public function isSuperAdmin()
    {
        return $this->user_type === 'super' || $this->role === 'super';
    }

    /**
     * Get the current hotel for the user.
     */
    public function getCurrentHotel()
    {
        $hotelId = session('current_hotel_id');
        if ($hotelId && $this->hasAccessToHotel($hotelId)) {
            return Hotel::find($hotelId);
        }

        // Return first hotel if no current hotel set
        return $this->hotels()->first();
    }

    /**
     * Set the current hotel for the user session.
     */
    public function setCurrentHotel($hotelId)
    {
        if ($this->isSuperAdmin() || $this->hasAccessToHotel($hotelId)) {
            session(['current_hotel_id' => $hotelId]);
            return true;
        }
        return false;
    }

    /**
     * Get user's roles for a specific hotel.
     */
    public function rolesForHotel($hotelId)
    {
        return $this->belongsToMany(Role::class, 'hotel_user_roles')
                    ->wherePivot('hotel_id', $hotelId)
                    ->wherePivot('is_active', true)
                    ->get();
    }

    /**
     * Check if user has a specific role in a hotel.
     */
    public function hasRoleInHotel($roleSlug, $hotelId)
    {
        return $this->rolesForHotel($hotelId)
                    ->where('slug', $roleSlug)
                    ->isNotEmpty();
    }

    /**
     * Check if user has a specific permission in current hotel.
     */
    public function hasPermission($permission, $hotelId = null)
    {
        if ($this->isSuperAdmin()) {
            return true;
        }

        $hotelId = $hotelId ?: session('current_hotel_id');
        if (!$hotelId) {
            return false;
        }

        $roles = $this->rolesForHotel($hotelId);

        foreach ($roles as $role) {
            if ($role->hasPermission($permission)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Assign role to user for a specific hotel.
     */
    public function assignRoleToHotel($roleId, $hotelId)
    {
        return DB::table('hotel_user_roles')->updateOrInsert(
            [
                'user_id' => $this->id,
                'hotel_id' => $hotelId,
                'role_id' => $roleId,
            ],
            [
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]
        );
    }

    /**
     * Remove role from user for a specific hotel.
     */
    public function removeRoleFromHotel($roleId, $hotelId)
    {
        return DB::table('hotel_user_roles')
                  ->where('user_id', $this->id)
                  ->where('hotel_id', $hotelId)
                  ->where('role_id', $roleId)
                  ->delete();
    }

    /**
     * Get all permissions for user in current hotel.
     */
    public function getAllPermissions($hotelId = null)
    {
        if ($this->isSuperAdmin()) {
            return ['*']; // Super admin has all permissions
        }

        $hotelId = $hotelId ?: session('current_hotel_id');
        if (!$hotelId) {
            return [];
        }

        $roles = $this->rolesForHotel($hotelId);
        $permissions = [];

        foreach ($roles as $role) {
            $permissions = array_merge($permissions, $role->permissions ?? []);
        }

        return array_unique($permissions);
    }
}
