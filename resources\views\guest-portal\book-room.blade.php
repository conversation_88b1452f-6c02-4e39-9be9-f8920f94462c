@extends('guest-portal.layout')

@section('title', 'Book a Room')

@section('header')
    <div class="text-center">
        <h1 class="display-5 fw-bold mb-3">Book Your Stay</h1>
        <p class="lead">{{ $hotel->name }} - Find the perfect room for your visit</p>
    </div>
@endsection

@section('content')
<!-- Search Form -->
<div class="row mb-4">
    <div class="col-lg-10 mx-auto">
        <div class="card border-hotel-primary">
            <div class="card-header bg-hotel-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-search me-2"></i>Search Available Rooms
                </h5>
            </div>
            <div class="card-body">
                <form id="searchForm">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label for="checkin" class="form-label">Check-in Date</label>
                            <input type="date" class="form-control" id="checkin" name="checkin" 
                                   value="{{ request('checkin', date('Y-m-d')) }}" min="{{ date('Y-m-d') }}" required>
                        </div>
                        <div class="col-md-3">
                            <label for="checkout" class="form-label">Check-out Date</label>
                            <input type="date" class="form-control" id="checkout" name="checkout" 
                                   value="{{ request('checkout', date('Y-m-d', strtotime('+1 day'))) }}" min="{{ date('Y-m-d', strtotime('+1 day')) }}" required>
                        </div>
                        <div class="col-md-2">
                            <label for="guests" class="form-label">Guests</label>
                            <select class="form-select" id="guests" name="guests">
                                <option value="1" {{ request('guests') == '1' ? 'selected' : '' }}>1 Guest</option>
                                <option value="2" {{ request('guests') == '2' ? 'selected' : '' }}>2 Guests</option>
                                <option value="3" {{ request('guests') == '3' ? 'selected' : '' }}>3 Guests</option>
                                <option value="4" {{ request('guests') == '4' ? 'selected' : '' }}>4 Guests</option>
                            </select>
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-success w-100">
                                <i class="fas fa-search me-2"></i>Search Rooms
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Loading Spinner -->
<div id="loadingSpinner" class="text-center d-none">
    <div class="spinner-border text-hotel-primary" role="status">
        <span class="visually-hidden">Loading...</span>
    </div>
    <p class="mt-2">Searching for available rooms...</p>
</div>

<!-- Available Rooms -->
<div id="roomResults" class="row">
    <!-- Results will be loaded here via AJAX -->
</div>

<!-- No Results Message -->
<div id="noResults" class="text-center d-none">
    <div class="card">
        <div class="card-body py-5">
            <i class="fas fa-bed fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">No rooms available</h4>
            <p class="text-muted">Please try different dates or contact us directly.</p>
            <a href="{{ route('guest-portal.hotel', $hotel->slug) }}" class="btn btn-hotel-primary">
                <i class="fas fa-arrow-left me-2"></i>Back to Hotel
            </a>
        </div>
    </div>
</div>

<!-- Booking Modal -->
<div class="modal fade" id="bookingModal" tabindex="-1" aria-labelledby="bookingModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bookingModalLabel">Complete Your Booking</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="bookingForm">
                <div class="modal-body">
                    <div id="bookingDetails"></div>
                    
                    <hr>
                    
                    <h6>Guest Information</h6>
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="guest_first_name" class="form-label">First Name *</label>
                            <input type="text" class="form-control" id="guest_first_name" name="guest_first_name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="guest_last_name" class="form-label">Last Name *</label>
                            <input type="text" class="form-control" id="guest_last_name" name="guest_last_name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="guest_email" class="form-label">Email Address *</label>
                            <input type="email" class="form-control" id="guest_email" name="guest_email" required>
                        </div>
                        <div class="col-md-6">
                            <label for="guest_phone" class="form-label">Phone Number *</label>
                            <input type="tel" class="form-control" id="guest_phone" name="guest_phone" required>
                        </div>
                        <div class="col-12">
                            <label for="special_requests" class="form-label">Special Requests (Optional)</label>
                            <textarea class="form-control" id="special_requests" name="special_requests" rows="3" 
                                      placeholder="Any special requests or notes for your stay..."></textarea>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-hotel-primary">
                        <i class="fas fa-check me-2"></i>Confirm Booking
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
let selectedRoom = null;
let searchDates = {};
let searchTimeout = null;

// Debounce function to prevent too many API calls
function debounceSearch() {
    if (searchTimeout) {
        clearTimeout(searchTimeout);
    }
    searchTimeout = setTimeout(searchRooms, 500); // Wait 500ms after user stops typing
}

// Auto-update checkout date when checkin changes and trigger search
document.getElementById('checkin').addEventListener('change', function() {
    const checkinDate = new Date(this.value);
    const checkoutDate = new Date(checkinDate);
    checkoutDate.setDate(checkoutDate.getDate() + 1);

    const checkoutInput = document.getElementById('checkout');
    checkoutInput.min = checkoutDate.toISOString().split('T')[0];

    if (new Date(checkoutInput.value) <= checkinDate) {
        checkoutInput.value = checkoutDate.toISOString().split('T')[0];
    }

    // Auto-search when dates change
    if (this.value && checkoutInput.value) {
        searchRooms();
    }
});

// Auto-search when checkout date changes
document.getElementById('checkout').addEventListener('change', function() {
    const checkinInput = document.getElementById('checkin');
    if (checkinInput.value && this.value) {
        searchRooms();
    }
});

// Auto-search when guest count changes (with debounce for typing)
document.getElementById('guests').addEventListener('input', function() {
    const checkinInput = document.getElementById('checkin');
    const checkoutInput = document.getElementById('checkout');
    if (checkinInput.value && checkoutInput.value) {
        debounceSearch();
    }
});

// Search form submission (for manual search button)
document.getElementById('searchForm').addEventListener('submit', function(e) {
    e.preventDefault();
    searchRooms();
});

// Search for available rooms
function searchRooms() {
    const formData = new FormData(document.getElementById('searchForm'));
    
    // Store search dates for booking
    searchDates = {
        checkin: formData.get('checkin'),
        checkout: formData.get('checkout'),
        guests: formData.get('guests')
    };
    
    // Show loading spinner
    document.getElementById('loadingSpinner').classList.remove('d-none');
    document.getElementById('roomResults').innerHTML = '';
    document.getElementById('noResults').classList.add('d-none');
    
    // Make AJAX request
    fetch('{{ route("guest-portal.available-rooms", $hotel->slug) }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        document.getElementById('loadingSpinner').classList.add('d-none');
        
        if (data.success && data.rooms.length > 0) {
            displayRooms(data.rooms);
        } else {
            document.getElementById('noResults').classList.remove('d-none');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        document.getElementById('loadingSpinner').classList.add('d-none');
        alert('Error searching for rooms. Please try again.');
    });
}

// Display available rooms
function displayRooms(rooms) {
    const resultsContainer = document.getElementById('roomResults');
    resultsContainer.innerHTML = '';
    
    rooms.forEach(room => {
        const roomCard = createRoomCard(room);
        resultsContainer.appendChild(roomCard);
    });
}

// Create room card element
function createRoomCard(room) {
    const col = document.createElement('div');
    col.className = 'col-lg-6 mb-4';
    
    const nights = calculateNights(searchDates.checkin, searchDates.checkout);
    const totalPrice = room.price * nights;
    
    col.innerHTML = `
        <div class="card h-100">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h5 class="card-title">${room.room_type_name}</h5>
                        <p class="text-muted mb-2">Room ${room.room_number}</p>
                        <p class="card-text">${room.description || 'Comfortable room with modern amenities'}</p>
                        <div class="mb-3">
                            <small class="text-muted">
                                <i class="fas fa-users me-1"></i>Max ${room.capacity} guests
                                <i class="fas fa-bed ms-3 me-1"></i>${room.bed_type || 'Standard bed'}
                            </small>
                        </div>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="price-section">
                            <div class="h4 text-hotel-primary">₱${room.price.toLocaleString()}</div>
                            <small class="text-muted">per night</small>
                            <hr>
                            <div class="fw-bold text-hotel-primary">Total: ₱${totalPrice.toLocaleString()}</div>
                            <small class="text-muted">${nights} night${nights > 1 ? 's' : ''}</small>
                        </div>
                        <button class="btn btn-hotel-primary mt-3" onclick="selectRoom(${room.id}, '${room.room_type_name}', ${room.price}, '${room.room_number}')">
                            <i class="fas fa-check me-2"></i>Select Room
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    return col;
}

// Calculate number of nights
function calculateNights(checkin, checkout) {
    const checkinDate = new Date(checkin);
    const checkoutDate = new Date(checkout);
    const timeDiff = checkoutDate.getTime() - checkinDate.getTime();
    return Math.ceil(timeDiff / (1000 * 3600 * 24));
}

// Select room for booking
function selectRoom(roomId, roomTypeName, price, roomNumber) {
    selectedRoom = {
        id: roomId,
        type_name: roomTypeName,
        price: price,
        room_number: roomNumber
    };
    
    const nights = calculateNights(searchDates.checkin, searchDates.checkout);
    const totalPrice = price * nights;
    
    // Update booking modal
    document.getElementById('bookingDetails').innerHTML = `
        <div class="card bg-light">
            <div class="card-body">
                <h6>Booking Summary</h6>
                <div class="row">
                    <div class="col-md-6">
                        <p class="mb-1"><strong>Room:</strong> ${roomTypeName} (${roomNumber})</p>
                        <p class="mb-1"><strong>Check-in:</strong> ${new Date(searchDates.checkin).toLocaleDateString()}</p>
                        <p class="mb-1"><strong>Check-out:</strong> ${new Date(searchDates.checkout).toLocaleDateString()}</p>
                    </div>
                    <div class="col-md-6">
                        <p class="mb-1"><strong>Guests:</strong> ${searchDates.guests}</p>
                        <p class="mb-1"><strong>Nights:</strong> ${nights}</p>
                        <p class="mb-1"><strong>Total:</strong> <span class="h5 text-hotel-primary">₱${totalPrice.toLocaleString()}</span></p>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('bookingModal'));
    modal.show();
}

// Handle booking form submission
document.getElementById('bookingForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    if (!selectedRoom) {
        alert('Please select a room first.');
        return;
    }
    
    const formData = new FormData(this);
    formData.append('room_id', selectedRoom.id);
    formData.append('checkin_date', searchDates.checkin);
    formData.append('checkout_date', searchDates.checkout);
    formData.append('guests', searchDates.guests);
    
    // Submit booking
    fetch('{{ route("guest-portal.store-booking", $hotel->slug) }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.href = data.redirect_url;
        } else {
            alert(data.message || 'Error creating booking. Please try again.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error creating booking. Please try again.');
    });
});

// Auto-search on page load if dates are provided
document.addEventListener('DOMContentLoaded', function() {
    // Get URL parameters and pre-fill form
    const urlParams = new URLSearchParams(window.location.search);

    if (urlParams.get('checkin')) {
        document.getElementById('checkin').value = urlParams.get('checkin');
    }
    if (urlParams.get('checkout')) {
        document.getElementById('checkout').value = urlParams.get('checkout');
    }
    if (urlParams.get('guests')) {
        document.getElementById('guests').value = urlParams.get('guests');
    }

    // Auto-search if we have the required dates
    const checkin = document.getElementById('checkin').value;
    const checkout = document.getElementById('checkout').value;

    if (checkin && checkout) {
        searchRooms();
    }
});
</script>
@endpush
