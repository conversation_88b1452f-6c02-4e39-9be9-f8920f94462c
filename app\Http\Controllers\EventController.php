<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Event;
use App\Models\Event_Item;
use App\Models\Menu;
use App\Models\Room;
use App\Models\RoomType;
use App\Models\Booking;
use App\Models\Invoice;
use App\Models\Grocery_Item;
use App\Models\Menu_Ingredient;
use App\Models\AdditionalCharge;
use App\Models\Stock_Movement;
use App\Models\Ingredient;
use Carbon\Carbon;
use App\Models\Guest;
use Illuminate\Support\Facades\Auth;

class EventController extends Controller
{
    public function index()
    {
        $events = Event::all();
        return view('admin.events.view-events', compact('events'));
    }

    public function add()
    {
        $menu_list = Menu::orderBy('name', 'asc')->get();//vince orig $menu_list = Menu::all();
        $room_types = RoomType::all();
        return view('admin.events.add-events', compact('room_types', 'menu_list'));
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'order_items' => 'nullable|array',
            'order_items.*.menu_id' => 'nullable|exists:menus,id',

            'customer_first_name' => 'required|string',
            'customer_last_name' => 'required|string',
            'customer_email' => 'nullable|email',
            'customer_phone' => 'required|string',

            'room_id' => 'required|exists:rooms,id',
            'name' => 'required|string',
            'description' => 'nullable|string',
            'start_date' => 'required|date',
            'end_date' => 'required|date',
            'created_by' => 'nullable|string',
            'status' => 'nullable|string',

            'sub_total' => 'nullable|numeric',
        ]);
        /*vince orig $request->validate([
            'order_items' => 'nullable|array',
            'order_items.*.menu_id' => 'nullable|exists:menus,id',

            'customer_first_name' => 'required|string',
            'customer_last_name' => 'required|string',
            'customer_email' => 'nullable|email',
            'customer_phone' => 'required|string',

            'room_id' => 'required|exists:rooms,id',
            'name' => 'required|string',
            'description' => 'nullable|string',
            'start_date' => 'required|date',
            'end_date' => 'required|date',
            'created_by' => 'nullable|string',
            'status' => 'nullable|string',

            'sub_total' => 'nullable|numeric',
        ]); */

        $event = Event::create([
            'customer_first_name' => $request->input('customer_first_name'),
            'customer_last_name' => $request->input('customer_last_name'),
            'customer_email' => $request->input('customer_email'),
            'customer_phone' => $request->input('customer_phone'),

            'room_id' => $request->input('room_id'),
            'name' => $request->input('name'),
            'description' => $request->input('description'),
            'start_date' => Carbon::createFromFormat('Y-m-d H:i',$request->input('start_date'))->format('Y-m-d H:i:s'),//vince added carbon
            'end_date' => Carbon::createFromFormat('Y-m-d H:i',$request->input('end_date'))->format('Y-m-d H:i:s'),//vince added carbon
            'created_by' => Auth::user()->name,//vince orig $request->input('created_by'),
            'status' => $request->input('status'),
            'sub_total' => $request->input('sub_total'),
        ]);

        //if order items are present, for each order item, create a new event item in event_items table
        if ($request->has('order_items')) {
            foreach ($request->input('order_items') as $order_item) {
                // Create the event item
                $eventItem = Event_Item::create([
                    'event_id' => $event->id,
                    'menu_id' => $order_item['menu_id'],
                    'quantity' => floatval($order_item['quantity']),//vince added floatval since input is now type text in view
                    'price' => $order_item['price'],
                ]);

                // Retrieve menu to create grocery items
                $menu = Menu::find($order_item['menu_id']);
                $ingredients = $menu->ingredients; // Assuming your Menu model has a 'ingredients' relationship

                // For each ingredient, create a Grocery_Item entry
                foreach ($ingredients as $ingredient) {
                    // Calculate the required quantity based on the order item quantity
                    $requiredQuantity = $ingredient->pivot->quantity * $order_item['quantity']; // Assuming you have a pivot table with 'quantity' field

                    Grocery_Item::create([
                        'menu_id' => $menu->id,
                        'event_id' => $event->id,
                        'event_item_id' => $eventItem->id,
                        'ingredient_id' => $ingredient->id,
                        'quantity' => $requiredQuantity,
                        'unit' => $ingredient->unit, // Assuming your Ingredient model has a 'unit' field
                        'cost' => $ingredient->cost, // Assuming you store cost per unit in the Ingredient model
                        'total_cost' => $ingredient->cost * $requiredQuantity,
                    ]);

                    //For each ingredient, update the stock's pending usage
                    $ingredient->pending_usage += $requiredQuantity;
                    $ingredient->save();

                    //For each ingredient, create a new stock movement
                    Stock_Movement::create([
                        'ingredient_id' => $ingredient->id,
                        'quantity' => $requiredQuantity,
                        'movement_type' => 'usage',
                        'reason' => 'Event: ' . $event->name . ' - ' . $menu->name,
                    ]);
                }
            }
        }
        //vince added block
        // Check if Guest already exists WHERE first_name, last_name, email, phone
        $guest = Guest::where('first_name', $validatedData['customer_first_name'])
        ->where('last_name', $validatedData['customer_last_name'])
        ->where('phone', $validatedData['customer_phone'])
        ->first();
        if (!$guest) {
            $guest = new Guest();
            $guest->first_name = $validatedData['customer_first_name'];
            $guest->last_name = $validatedData['customer_last_name'];
            $guest->email = $validatedData['customer_email'];
            $guest->phone = $validatedData['customer_phone'];
            $guest->save();
        }
        $guest_id = $guest->id;
        //vince end

        //Create a new booking for the event
        $booking = Booking::create([
            'guest_first_name' => $event->customer_first_name,
            'guest_last_name' => $event->customer_last_name,
            'guest_email' => $event->customer_email,
            'guest_phone' => $event->customer_phone,
            'guest_id' => $guest_id,
            'room_id' => $event->room_id,
            'checkin_date' => $event->start_date,
            'checkout_date' => $event->end_date,
            'total_price' => $event->sub_total,
            'booking_status' => 'booked',
            'booked_by' => $event->created_by,
            'notes' =>'For the event: ' . $validatedData['name'] //added by Vince
        ]);

        //Update the event with the booking id
        $event->update([
            'booking_id' => $booking->id,
        ]);

        //Create a new invoice for the booking
        $invoice = Invoice::create([
            'booking_id' => $booking->id,
            'total_amount' => $booking->total_price,
            'invoice_date' => now(),
            'status' => 'unpaid',
            'created_by' => $event->created_by
        ]);

        //if the event has order items, update invoice total amount and insert new order items as additional charges in AdditionalCharges table
        if ($request->has('order_items')) {
            $additionalCharges = 0;
            foreach ($request->input('order_items') as $order_item) {
                $additionalCharges += $order_item['price'];
            }

            //Insert order items as additional charges
            foreach ($request->input('order_items') as $order_item) {
                //get order item name
                $menu = Menu::find($order_item['menu_id']);
                AdditionalCharge::create([
                    'invoice_id' => $invoice->id,
                    'description' => 'Event Order Item: ' . $menu->name . ' (Already Included in Sub-Total)',
                    'amount' => 0,
                ]);
            }
        }

        return redirect()->route('admin.events')->with('success', 'Event created successfully.');
    }

    public function edit($id)
    {
        $event = Event::find($id);
        if ($event) {
            //vince $room_types = RoomType::all();
            $rooms = Room::all();
            $menu_list = Menu::orderBy('name', 'asc')->get();//vince orig Menu::all();
            return view('admin.events.edit-events', with(['event' => $event, 'rooms' => $rooms, 'menu_list'=> $menu_list]));
        } else {
            return redirect()->route('admin.events')->with('error', 'Event not found.');
        }
        
    }

    public function update(Request $request, $id) {//vince added(filled) function
        $validatedData = $request->validate([
            'order_items' => 'nullable|array',
            'order_items.*.menu_id' => 'nullable|exists:menus,id',

            'customer_first_name' => 'required|string',
            'customer_last_name' => 'required|string',
            'customer_email' => 'nullable|email',
            'customer_phone' => 'required|string',

            'room_id' => 'required|exists:rooms,id',
            'name' => 'required|string',
            'description' => 'nullable|string',
            'start_date' => 'required|date',
            'end_date' => 'required|date',
            'created_by' => 'nullable|string',
            'status' => 'nullable|string',

            'sub_total' => 'nullable|numeric',
            'notes' => 'nullable|string'
        ]);

        $event = Event::findOrFail($id);
        
        if ($event) {
            $validatedNewData = [
                'customer_first_name' => $validatedData['customer_first_name'],
                'customer_last_name' => $validatedData['customer_last_name'],
                'customer_email' => $validatedData['customer_email'],
                'customer_phone' => $validatedData['customer_phone'],
    
                'room_id' => $validatedData['room_id'],
                'name' => $validatedData['name'],
                'description' => $validatedData['description'],
                'start_date' => Carbon::createFromFormat('Y-m-d H:i',$validatedData['start_date'])->format('Y-m-d H:i:s'),//vince added carbon
                'end_date' => Carbon::createFromFormat('Y-m-d H:i',$validatedData['end_date'])->format('Y-m-d H:i:s'),//vince added carbon
                'created_by' => Auth::user()->name,
                'status' => $validatedData['status'],
                'sub_total' => $validatedData['sub_total'],
            ];

            $event->update($validatedNewData);
            $event->eventItems()->delete(); // Delete existing items
            $event->groceries()->delete();
            if ($request->has('order_items')) {
                // Recreate each order item
                foreach ($request->input('order_items') as $order_item) {
                    // Create the event item
                    $eventItem = Event_Item::create([
                        'event_id' => $event->id,
                        'menu_id' => $order_item['menu_id'],
                        'quantity' => floatval($order_item['quantity']),//vince added floatval since input is now type text in view
                        'price' => $order_item['price'],
                    ]);
                
                    // Retrieve menu to create grocery items
                    $menu = Menu::find($order_item['menu_id']);
                    $ingredients = $menu->ingredients; // Assuming your Menu model has a 'ingredients' relationship

                    // For each ingredient, create a Grocery_Item entry
                    foreach ($ingredients as $ingredient) {
                        // Calculate the required quantity based on the order item quantity
                        $requiredQuantity = $ingredient->pivot->quantity * $order_item['quantity']; // Assuming you have a pivot table with 'quantity' field
                        Grocery_Item::create([
                            'menu_id' => $menu->id,
                            'event_id' => $event->id,
                            'event_item_id' => $eventItem->id,
                            'ingredient_id' => $ingredient->id,
                            'quantity' => $requiredQuantity,
                            'unit' => $ingredient->unit, // Assuming your Ingredient model has a 'unit' field
                            'cost' => $ingredient->cost, // Assuming you store cost per unit in the Ingredient model
                            'total_cost' => $ingredient->cost * $requiredQuantity,
                        ]);


                        //For each ingredient, update the stock's pending usage
                        $ingredient->pending_usage += $requiredQuantity;
                        $ingredient->save();

                        //For each ingredient, create a new stock movement
                        Stock_Movement::create([
                            'ingredient_id' => $ingredient->id,
                            'quantity' => $requiredQuantity,
                            'movement_type' => 'usage',
                            'reason' => 'Event: ' . $event->name . ' - ' . $menu->name,
                        ]);
                    }
                }
            } 
            
            // Check if Guest already exists WHERE first_name, last_name, email, phone
            $guest = Guest::where('first_name', $validatedData['customer_first_name'])
            ->where('last_name', $validatedData['customer_last_name'])
            ->where('phone', $validatedData['customer_phone'])
            ->first();
            if (!$guest) {
                $guest = new Guest();
                $guest->first_name = $validatedData['customer_first_name'];
                $guest->last_name = $validatedData['customer_last_name'];
                $guest->email = $validatedData['customer_email'];
                $guest->phone = $validatedData['customer_phone'];
                $guest->save();
            }
            $guest_id = $guest->id;
            

            //Update booking for the event
            $booking = Booking::findOrFail($event->booking->id);
            $new_booking_data = [
                'guest_first_name' => $event->customer_first_name,
                'guest_last_name' => $event->customer_last_name,
                'guest_email' => $event->customer_email,
                'guest_phone' => $event->customer_phone,
                'guest_id' => $guest_id,
                'room_id' => $event->room_id,
                'checkin_date' => $event->start_date,
                'checkout_date' => $event->end_date,
                'total_price' => $event->sub_total,
                'booking_status' => 'booked',
                'booked_by' => $event->created_by,
            ];
            // Update booking details
            $booking->fill($new_booking_data);
            $booking->save();


            //Update invoice for the booking
            $invoice = $event->booking->invoices->first();
            $invoice->fill([
                'booking_id' => $booking->id,
                'total_amount' => $booking->total_price,
                'invoice_date' => now(),
                'status' => 'unpaid',
                'created_by' => $event->created_by
            ]);
            $invoice->save();

            //if the event has order items, update invoice total amount and insert new order items as additional charges in AdditionalCharges table
            if ($event->eventItems()) {
                $additionalCharges = 0;
                foreach ($event->eventItems() as $order_item) {
                    $additionalCharges += $order_item['price'];
                }



                //Insert order items as additional charges
                foreach ($event->eventItems() as $order_item) {
                    //get order item name
                    $menu = Menu::find($order_item['menu_id']);
                    AdditionalCharge::create([
                        'invoice_id' => $invoice->id,
                        'description' => 'Event Order Item: ' . $menu->name . ' (Already Included in Sub-Total)',
                        'amount' => 0,
                    ]);
                }
            }

            return redirect()->route('admin.event.edit', $event->id)->with('success', 'Event updated successfully.');
        } else {
            return redirect()->route('admin.events')->with('error', 'Event not found.');
        }
        
    }

    public function destroy($id)
    {
        return redirect()->route('admin.events')->with('error', 'Event delete disabled.');
        /*vince $event = Event::findOrFail($id);
        $event->delete();

        return redirect()->route('events.index')->with('success', 'Event deleted successfully.'); */
    }

    public function completeEvent($eventId)
    {
        //check if event status is completed
        $event = Event::find($eventId);
        if ($event->status == 'completed') {
            return redirect()->route('admin.events')->with('error', 'Event already completed.');
            //return response()->json(['message' => 'Event already completed']);
        }

        //Check if it still has pending grocery items (is_bought = 0)
        $pendingGroceryItems = Grocery_Item::where('event_id', $eventId)->where('is_bought', 0)->get();
        if (!$pendingGroceryItems->isEmpty()) {
            return redirect()->route('admin.events')->with('error', 'Event still has pending grocery items.');
            //return response()->json(['message' => 'Event still has pending grocery items']);
        }

        // Fetch all grocery items for the event
        $groceryItems = Grocery_Item::where('event_id', $eventId)->get();

        foreach ($groceryItems as $item) {
            $ingredient = Ingredient::find($item->ingredient_id);

            if ($ingredient) {
                // Deduct pending usage from stock
                $ingredient->stock -= $item->quantity;
                $ingredient->pending_usage -= $item->quantity;
                $ingredient->save();

                //add stock movement record
                Stock_Movement::create([
                    'ingredient_id' => $item->ingredient_id,
                    'quantity' => $item->quantity,
                    'movement_type' => 'restock',
                    'reason' => 'Event: ' . $event->name . ' - Restock',
                ]);
            }
        }





        // Update event status to 'completed'
        Event::where('id', $eventId)->update(['status' => 'completed']);
        return redirect()->route('admin.events')->with('success', 'Event completed and stock updated.');

        //return response()->json(['message' => 'Event completed and stock updated']);
    }
}
