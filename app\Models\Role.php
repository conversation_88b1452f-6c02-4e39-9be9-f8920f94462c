<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Role extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'is_system_role',
        'permissions'
    ];

    protected $casts = [
        'permissions' => 'array',
        'is_system_role' => 'boolean',
    ];

    // Relationships
    public function users()
    {
        return $this->belongsToMany(User::class, 'hotel_user_roles')
                    ->withPivot('hotel_id', 'is_active')
                    ->withTimestamps();
    }

    public function hotels()
    {
        return $this->belongsToMany(Hotel::class, 'hotel_user_roles')
                    ->withPivot('user_id', 'is_active')
                    ->withTimestamps();
    }

    // Scopes
    public function scopeSystemRoles($query)
    {
        return $query->where('is_system_role', true);
    }

    public function scopeCustomRoles($query)
    {
        return $query->where('is_system_role', false);
    }

    // Helper methods
    public function hasPermission($permission)
    {
        $permissions = $this->permissions ?? [];
        
        // Check for wildcard permission
        if (in_array('*', $permissions)) {
            return true;
        }
        
        // Check for exact permission
        if (in_array($permission, $permissions)) {
            return true;
        }
        
        // Check for wildcard patterns (e.g., 'bookings.*' matches 'bookings.create')
        foreach ($permissions as $perm) {
            if (str_ends_with($perm, '.*')) {
                $prefix = substr($perm, 0, -2);
                if (str_starts_with($permission, $prefix . '.')) {
                    return true;
                }
            }
        }
        
        return false;
    }

    public function addPermission($permission)
    {
        $permissions = $this->permissions ?? [];
        
        if (!in_array($permission, $permissions)) {
            $permissions[] = $permission;
            $this->update(['permissions' => $permissions]);
        }
    }

    public function removePermission($permission)
    {
        $permissions = $this->permissions ?? [];
        $permissions = array_filter($permissions, fn($p) => $p !== $permission);
        
        $this->update(['permissions' => array_values($permissions)]);
    }

    public function getPermissionGroupsAttribute()
    {
        $permissions = $this->permissions ?? [];
        $groups = [];
        
        foreach ($permissions as $permission) {
            if ($permission === '*') {
                $groups['system'] = $groups['system'] ?? [];
                $groups['system'][] = $permission;
                continue;
            }
            
            $parts = explode('.', $permission);
            $group = $parts[0];
            
            $groups[$group] = $groups[$group] ?? [];
            $groups[$group][] = $permission;
        }
        
        return $groups;
    }

    // Static methods for common roles
    public static function superAdmin()
    {
        return static::where('slug', 'super-admin')->first();
    }

    public static function hotelAdmin()
    {
        return static::where('slug', 'hotel-admin')->first();
    }

    public static function manager()
    {
        return static::where('slug', 'manager')->first();
    }

    public static function frontDesk()
    {
        return static::where('slug', 'front-desk')->first();
    }

    public static function housekeeping()
    {
        return static::where('slug', 'housekeeping')->first();
    }

    public static function restaurantStaff()
    {
        return static::where('slug', 'restaurant-staff')->first();
    }

    public static function accountant()
    {
        return static::where('slug', 'accountant')->first();
    }

    public static function guest()
    {
        return static::where('slug', 'guest')->first();
    }

    // Get all available permissions
    public static function getAllPermissions()
    {
        return [
            'hotels' => [
                'hotels.manage' => 'Manage all hotels (Super Admin only)',
                'hotel.manage' => 'Manage current hotel',
            ],
            'users' => [
                'users.manage' => 'Manage users',
                'users.view' => 'View users',
                'users.create' => 'Create users',
                'users.edit' => 'Edit users',
                'users.delete' => 'Delete users',
            ],
            'rooms' => [
                'rooms.manage' => 'Full room management',
                'rooms.view' => 'View rooms',
                'rooms.create' => 'Create rooms',
                'rooms.edit' => 'Edit rooms',
                'rooms.delete' => 'Delete rooms',
                'rooms.update_status' => 'Update room status',
            ],
            'bookings' => [
                'bookings.manage' => 'Full booking management',
                'bookings.view' => 'View all bookings',
                'bookings.view_own' => 'View own bookings only',
                'bookings.create' => 'Create bookings',
                'bookings.edit' => 'Edit bookings',
                'bookings.cancel' => 'Cancel bookings',
                'checkin.manage' => 'Manage check-ins',
                'checkout.manage' => 'Manage check-outs',
            ],
            'guests' => [
                'guests.manage' => 'Full guest management',
                'guests.view' => 'View guests',
                'guests.create' => 'Create guests',
                'guests.edit' => 'Edit guests',
                'guests.delete' => 'Delete guests',
            ],
            'payments' => [
                'payments.manage' => 'Full payment management',
                'payments.view' => 'View payments',
                'payments.create' => 'Process payments',
                'payments.refund' => 'Process refunds',
            ],
            'invoices' => [
                'invoices.manage' => 'Full invoice management',
                'invoices.view' => 'View invoices',
                'invoices.create' => 'Create invoices',
                'invoices.edit' => 'Edit invoices',
            ],
            'restaurant' => [
                'restaurant.manage' => 'Full restaurant management',
                'orders.manage' => 'Manage orders',
                'menu.manage' => 'Manage menu',
                'menu.view' => 'View menu',
                'ingredients.manage' => 'Manage ingredients',
                'ingredients.view' => 'View ingredients',
            ],
            'events' => [
                'events.manage' => 'Full event management',
                'events.view' => 'View events',
                'events.create' => 'Create events',
                'events.edit' => 'Edit events',
            ],
            'reports' => [
                'reports.all' => 'Access all reports',
                'reports.view' => 'View basic reports',
                'reports.financial' => 'View financial reports',
            ],
            'settings' => [
                'settings.manage' => 'Manage hotel settings',
                'system.settings' => 'Manage system settings',
                'branding.manage' => 'Manage branding',
            ],
            'subscriptions' => [
                'subscriptions.manage' => 'Manage subscriptions',
                'billing.manage' => 'Manage billing',
            ],
            'maintenance' => [
                'maintenance.manage' => 'Manage maintenance',
            ],
            'expenses' => [
                'expenses.manage' => 'Manage expenses',
                'remittances.manage' => 'Manage remittances',
            ],
            'profile' => [
                'profile.manage' => 'Manage own profile',
            ]
        ];
    }
}
