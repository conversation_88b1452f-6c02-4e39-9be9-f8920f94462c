@extends('guest-portal.layout')

@section('title', 'Payment')

@section('header')
    <div class="text-center">
        <h1 class="display-5 fw-bold mb-3">Payment</h1>
        <p class="lead">Invoice #{{ $invoice->id }} - {{ $hotel->name }}</p>
    </div>
@endsection

@section('content')
<div class="row justify-content-center">
    <div class="col-lg-8">
        <!-- Invoice Summary -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-invoice me-2"></i>Invoice Summary
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Invoice Details</h6>
                        <p class="mb-1"><strong>Invoice #:</strong> {{ $invoice->id }}</p>
                        <p class="mb-1"><strong>Date:</strong> {{ \Carbon\Carbon::parse($invoice->invoice_date)->format('M d, Y') }}</p>
                        <p class="mb-1"><strong>Status:</strong> 
                            <span class="badge bg-{{ $invoice->status === 'paid' ? 'success' : 'warning' }}">
                                {{ ucfirst($invoice->status) }}
                            </span>
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h6>Booking Details</h6>
                        @if($invoice->booking)
                            <p class="mb-1"><strong>Booking #:</strong> {{ $invoice->booking->id }}</p>
                            <p class="mb-1"><strong>Room:</strong> {{ $invoice->booking->room->room_number ?? 'N/A' }}</p>
                            <p class="mb-1"><strong>Guest:</strong> {{ $invoice->booking->guest_first_name }} {{ $invoice->booking->guest_last_name }}</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Breakdown -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calculator me-2"></i>Payment Breakdown
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <tbody>
                            @if($invoice->booking)
                            <tr>
                                <td>Room Charges</td>
                                <td class="text-end">₱{{ number_format($invoice->booking->total_price, 2) }}</td>
                            </tr>
                            @endif
                            
                            @foreach($invoice->additionalCharges as $charge)
                            <tr>
                                <td>{{ $charge->description }}</td>
                                <td class="text-end">₱{{ number_format($charge->amount, 2) }}</td>
                            </tr>
                            @endforeach
                            
                            <tr class="table-active">
                                <td><strong>Total Amount</strong></td>
                                <td class="text-end"><strong>₱{{ number_format($invoice->total_amount, 2) }}</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Payment Methods -->
        @if($invoice->status !== 'paid')
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-credit-card me-2"></i>Choose Payment Method
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <!-- PayMongo Payment -->
                    <div class="col-md-6">
                        <div class="card border-hotel-primary h-100">
                            <div class="card-body text-center">
                                <div class="text-hotel-primary mb-3">
                                    <i class="fas fa-mobile-alt fa-3x"></i>
                                </div>
                                <h5 class="card-title">Digital Wallets & Cards</h5>
                                <p class="card-text small">
                                    Pay using GCash, PayMaya, GrabPay, Credit/Debit Cards, or QR Ph
                                </p>
                                <a href="{{ route('guest-portal.payment-paymongo', [$hotel->slug, $invoice->id]) }}" 
                                   class="btn btn-hotel-primary">
                                    <i class="fas fa-mobile-alt me-2"></i>Pay Now
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- PayPal Payment -->
                    <div class="col-md-6">
                        <div class="card border-primary h-100">
                            <div class="card-body text-center">
                                <div class="text-primary mb-3">
                                    <i class="fab fa-paypal fa-3x"></i>
                                </div>
                                <h5 class="card-title">PayPal</h5>
                                <p class="card-text small">
                                    Secure payment with PayPal account or credit card
                                </p>
                                <a href="{{ route('guest-portal.payment-paypal', [$hotel->slug, $invoice->id]) }}" 
                                   class="btn btn-primary">
                                    <i class="fab fa-paypal me-2"></i>Pay with PayPal
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="alert alert-info mt-4">
                    <i class="fas fa-shield-alt me-2"></i>
                    <strong>Secure Payment:</strong> All payments are processed securely using industry-standard encryption. 
                    Your payment information is never stored on our servers.
                </div>
            </div>
        </div>
        @else
        <!-- Payment Complete -->
        <div class="card border-success">
            <div class="card-body text-center">
                <div class="text-success mb-3">
                    <i class="fas fa-check-circle fa-4x"></i>
                </div>
                <h4 class="text-success">Payment Complete!</h4>
                <p class="text-muted">This invoice has been paid in full.</p>
                
                @if($invoice->payments->count() > 0)
                <div class="mt-4">
                    <h6>Payment History</h6>
                    @foreach($invoice->payments as $payment)
                    <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                        <div>
                            <strong>{{ ucfirst($payment->payment_method) }}</strong>
                            <br>
                            <small class="text-muted">{{ \Carbon\Carbon::parse($payment->payment_date)->format('M d, Y g:i A') }}</small>
                        </div>
                        <div class="text-end">
                            <strong>₱{{ number_format($payment->amount, 2) }}</strong>
                        </div>
                    </div>
                    @endforeach
                </div>
                @endif
            </div>
        </div>
        @endif

        <!-- Navigation -->
        <div class="text-center mt-4">
            <a href="{{ route('guest-portal.invoice', [$hotel->slug, $invoice->id]) }}" class="btn btn-outline-secondary me-2">
                <i class="fas fa-arrow-left me-2"></i>Back to Invoice
            </a>
            <a href="{{ route('guest-portal.dashboard', $hotel->slug) }}" class="btn btn-outline-primary">
                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
            </a>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.payment-method-card {
    transition: transform 0.2s, box-shadow 0.2s;
    cursor: pointer;
}

.payment-method-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
</style>
@endpush
