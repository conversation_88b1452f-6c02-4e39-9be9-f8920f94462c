<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\BelongsToHotel;

class AdditionalCharge extends Model
{
    use HasFactory, BelongsToHotel;

    protected $table = 'additional_charges';
    protected $fillable = [
        'hotel_id',
        'invoice_id',
        'order_id', //vince added
        'description',
        'amount',

    ];

    public function order() {//vince added function
        return $this->hasOne(Order::class);
    }

    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }
}
