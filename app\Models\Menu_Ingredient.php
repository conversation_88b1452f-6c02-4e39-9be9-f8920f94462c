<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\BelongsToHotel;

class Menu_Ingredient extends Model
{
    use HasFactory, BelongsToHotel;

    protected $table = 'menu_ingredients'; // Specify the custom table name
    protected $fillable = [
        'id',
        'hotel_id',
        'menu_id',
        'ingredient_id',
        'quantity',
        'unit',
        'created_at',
        'updated_at',
    ];

    public function menu()
    {
        return $this->belongsTo(Menu::class);
    }

    public function ingredient()
    {
        return $this->belongsTo(Ingredient::class);
    }
}
