# Guest Portal Feature Guide

## Overview

The Guest Portal is a new feature that allows hotel guests to access their bookings and pay invoices using their own devices. This eliminates the need for dual-screen setups at the front desk and provides a better customer experience.

## Features

### For Guests
- **Mobile-Friendly Access**: View bookings and invoices on any device
- **Secure Authentication**: Access using email + booking/invoice reference
- **Online Payments**: Pay invoices using PayPal, GCash, PayMaya, and other payment methods
- **Real-Time Booking**: Make new room reservations online
- **Invoice Management**: View detailed invoices and payment history

### For Hotel Staff
- **QR Code Generation**: Generate QR codes for easy guest access
- **Guest Link Sharing**: Create secure links to share with guests
- **Payment Tracking**: Monitor guest portal payments in the admin panel
- **Seamless Integration**: Works with existing booking and invoice systems

## How to Use

### For Hotel Staff

#### 1. Generate Guest Access Links
1. Go to **Admin > Invoices**
2. Find the invoice you want to share with a guest
3. Click the **Link** button (🔗) in the Actions column
4. Copy the generated link and share it with the guest via email, SMS, or messaging

#### 2. Generate QR Codes
1. In the **Admin > Invoices** page, click on any QR code option:
   - **Guest Portal**: Creates a QR code that takes guests directly to the payment page
   - **PayPal QR**: Direct PayPal payment QR code
   - **PayMongo QR**: Direct PayMongo payment QR code

#### 3. Share Access Information
When sharing access with guests, provide them with:
- Their email address (used for booking)
- Either their booking reference number OR invoice number
- The guest portal link or QR code

### For Guests

#### 1. Accessing the Portal
**Option A: Using QR Code**
- Scan the QR code provided by hotel staff
- This will take you directly to the payment page

**Option B: Using Access Link**
- Click the link provided by hotel staff
- Enter your email and booking/invoice reference

**Option C: Manual Access**
- Visit the hotel's portal page
- Click "Guest Access"
- Enter your email and booking/invoice reference

#### 2. Viewing Your Information
Once logged in, you can:
- View all your bookings with the hotel
- See booking details (room, dates, status)
- Access invoices and payment history
- Make new bookings

#### 3. Making Payments
1. Click on any unpaid invoice
2. Review the payment breakdown
3. Choose your preferred payment method:
   - **Digital Wallets**: GCash, PayMaya, GrabPay
   - **Credit/Debit Cards**: Visa, Mastercard, etc.
   - **PayPal**: Secure PayPal payments
4. Complete the payment process
5. Receive confirmation

## Technical Details

### Routes
- **Hotel Portal**: `/portal/{hotel-slug}`
- **Guest Access**: `/portal/{hotel-slug}/access`
- **Payment Page**: `/portal/{hotel-slug}/invoice/{invoice-id}/pay`
- **QR Codes**: `/portal/qr/guest-payment/{invoice-id}`

### Security Features
- **Session-Based Authentication**: Secure 24-hour sessions
- **Email Verification**: Access requires valid email + reference
- **Hotel Isolation**: Guests can only access their own hotel's data
- **Secure Payments**: All payments processed through encrypted gateways

### Integration Points
- **Existing Booking System**: Uses current booking and guest models
- **Payment Gateways**: Integrates with PayPal and PayMongo
- **Hotel Branding**: Automatically applies hotel colors and logos
- **Admin Panel**: Seamless integration with existing admin features

## Benefits

### For Hotels
- **Reduced Front Desk Load**: Guests can self-serve for payments
- **No Hardware Requirements**: Works on guest devices, no dual screens needed
- **Better Customer Experience**: Modern, mobile-friendly interface
- **Increased Payment Options**: More ways for guests to pay
- **Real-Time Updates**: Payments reflect immediately in the system

### For Guests
- **Convenience**: Pay from anywhere, anytime
- **Security**: Secure payment processing
- **Transparency**: Clear invoice breakdown and payment history
- **Mobile Friendly**: Works on phones, tablets, and computers
- **Multiple Payment Options**: Choose preferred payment method

## Troubleshooting

### Common Issues

**Guest Can't Access Portal**
- Verify email address matches booking records
- Check booking/invoice reference number
- Ensure the booking has an associated email

**Payment Not Processing**
- Check internet connection
- Verify payment method details
- Try alternative payment method
- Contact hotel staff for assistance

**QR Code Not Working**
- Ensure QR code is generated for correct invoice
- Check that invoice has associated guest email
- Verify hotel slug is correct in URL

### Support
For technical issues or questions:
1. Check this documentation first
2. Contact hotel staff for guest-related issues
3. Check admin panel for payment status updates

## Future Enhancements

Planned features for future releases:
- Email notifications for booking confirmations
- SMS integration for access codes
- Guest profile management
- Loyalty program integration
- Multi-language support
- Advanced reporting for guest portal usage
