<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('menus', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('description')->nullable();
            $table->text('how_to_cook')->nullable();
            $table->float('price');
            $table->string('cook_time')->nullable();
            $table->timestamps();
        });

        //Insert Dummy Data
        DB::table('menus')->insert([
            [
                'name' => 'Fried Rice',
                'description' => 'Fried Rice with Chicken',
                'how_to_cook' => 'Fry the rice with chicken',
                'price' => 10.00,
                'cook_time' => '10 minutes',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Garlic Rice',
                'description' => 'Jollof Rice with Chicken',
                'how_to_cook' => 'Cook the rice with chicken',
                'price' => 15.00,
                'cook_time' => '15 minutes',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Spaghetti',
                'description' => 'Spaghetti with Chicken',
                'how_to_cook' => 'Boil the spaghetti with chicken',
                'price' => 50.00,
                'cook_time' => '30 minutes',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Corned Beef',
                'description' => 'Sauteed Argentina corned beef',
                'how_to_cook' => 'Mix spice and corned beef in pan, boil',
                'price' => 59.00,
                'cook_time' => '10 minutes',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Kusahos',
                'description' => 'Beef tapa special',
                'how_to_cook' => 'Stir Fry in oil',
                'price' => 69.00,
                'cook_time' => '10 minutes',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Scrambled Eggs',
                'description' => 'Scrambled eggs',
                'how_to_cook' => 'Fry in pan',
                'price' => 25.00,
                'cook_time' => '5 minutes',
                'created_at' => now(),
                'updated_at' => now(),
            ]
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('menus');
    }
};
