<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('hotels', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->string('email')->unique();
            $table->string('phone')->nullable();
            $table->text('address')->nullable();
            $table->string('tin')->nullable();
            $table->text('description')->nullable();
            $table->string('logo')->nullable();
            $table->string('website')->nullable();
            $table->enum('status', ['active', 'inactive', 'suspended'])->default('active');
            $table->json('settings')->nullable(); // Store hotel-specific settings
            $table->timestamp('trial_ends_at')->nullable();
            $table->timestamp('subscription_ends_at')->nullable();
            $table->timestamps();
        });

        // Create default hotel from existing settings
        $defaultHotel = [
            'name' => 'Laravel Hotel',
            'slug' => 'laravel-hotel',
            'email' => '<EMAIL>',
            'phone' => '1224565',
            'address' => 'Hollywood Blvd',
            'tin' => '111-222-333-444',
            'description' => 'Laravel Hotel - Best Hotel in Town',
            'status' => 'active',
            'created_at' => now(),
            'updated_at' => now(),
        ];

        DB::table('hotels')->insert($defaultHotel);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('hotels');
    }
};
