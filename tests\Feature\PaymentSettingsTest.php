<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Hotel;
use App\Models\Setting;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

class PaymentSettingsTest extends TestCase
{
    use RefreshDatabase;

    protected $hotel;
    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test hotel
        $this->hotel = Hotel::create([
            'name' => 'Test Hotel',
            'slug' => 'test-hotel',
            'email' => '<EMAIL>',
            'status' => 'active'
        ]);

        // Create a test user
        $this->user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'admin'
        ]);

        // Associate user with hotel
        $this->hotel->users()->attach($this->user->id, [
            'role' => 'admin',
            'is_active' => true
        ]);

        // Set current hotel in session
        session(['current_hotel_id' => $this->hotel->id]);
    }

    public function test_payment_settings_page_loads()
    {
        $response = $this->actingAs($this->user)
            ->get('/admin/settings/payment');

        $response->assertStatus(200);
        $response->assertSee('Payment Settings');
        $response->assertSee('PayPal Configuration');
        $response->assertSee('PayMongo Configuration');
    }

    public function test_payment_settings_can_be_updated()
    {
        $paymentData = [
            'paypal_client_id' => 'test_paypal_client_id',
            'paypal_client_secret' => 'test_paypal_secret',
            'paypal_mode' => 'live',
            'paypal_currency' => 'USD',
            'paypal_service_fee' => '3.5',
            'paymongo_public_key' => 'pk_test_123',
            'paymongo_secret_key' => 'sk_test_456',
            'paymongo_service_fee' => '4.0',
        ];

        $response = $this->actingAs($this->user)
            ->post('/admin/settings/update', $paymentData);

        $response->assertRedirect();
        $response->assertSessionHas('success', 'Settings updated successfully.');

        // Verify settings were saved
        foreach ($paymentData as $key => $value) {
            $setting = Setting::where('hotel_id', $this->hotel->id)
                ->where('key', $key)
                ->first();
            
            $this->assertNotNull($setting, "Setting {$key} was not created");
            $this->assertEquals($value, $setting->value, "Setting {$key} has incorrect value");
        }
    }

    public function test_payment_settings_are_hotel_specific()
    {
        // Create another hotel
        $hotel2 = Hotel::create([
            'name' => 'Test Hotel 2',
            'slug' => 'test-hotel-2',
            'email' => '<EMAIL>',
            'status' => 'active'
        ]);

        // Create settings directly for each hotel
        Setting::create([
            'hotel_id' => $this->hotel->id,
            'key' => 'paypal_client_id',
            'value' => 'hotel1_paypal_id'
        ]);

        Setting::create([
            'hotel_id' => $this->hotel->id,
            'key' => 'paymongo_secret_key',
            'value' => 'hotel1_paymongo_secret'
        ]);

        Setting::create([
            'hotel_id' => $hotel2->id,
            'key' => 'paypal_client_id',
            'value' => 'hotel2_paypal_id'
        ]);

        Setting::create([
            'hotel_id' => $hotel2->id,
            'key' => 'paymongo_secret_key',
            'value' => 'hotel2_paymongo_secret'
        ]);

        // Verify each hotel has its own settings (bypass hotel scope)
        $hotel1PaypalId = Setting::withoutHotelScope()
            ->where('hotel_id', $this->hotel->id)
            ->where('key', 'paypal_client_id')
            ->value('value');

        $hotel2PaypalId = Setting::withoutHotelScope()
            ->where('hotel_id', $hotel2->id)
            ->where('key', 'paypal_client_id')
            ->value('value');

        $this->assertEquals('hotel1_paypal_id', $hotel1PaypalId);
        $this->assertEquals('hotel2_paypal_id', $hotel2PaypalId);

        // Test that get_setting function works correctly for each hotel
        session(['current_hotel_id' => $this->hotel->id]);
        $this->assertEquals('hotel1_paypal_id', get_setting('paypal_client_id'));

        session(['current_hotel_id' => $hotel2->id]);
        $this->assertEquals('hotel2_paypal_id', get_setting('paypal_client_id'));
    }
}
