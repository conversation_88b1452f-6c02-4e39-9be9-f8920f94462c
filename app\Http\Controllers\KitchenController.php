<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Event;
use App\Models\Event_Item;
use App\Models\Menu;
use Carbon\Carbon;

class KitchenController extends Controller
{
    public function kitchenView()
    {
        return view('admin.kitchen.kitchen-view');
    }

    public function search(Request $request)
    {
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);

        $start_date = Carbon::parse($request->input('start_date'))->startOfDay();
        $end_date = Carbon::parse($request->input('end_date'))->endOfDay();

        // Fetch events within the date range
        $events = Event::whereBetween('start_date', [$start_date, $end_date])->get();

        // Prepare events with their menu items
        $eventsWithMenus = [];

        foreach ($events as $event) {
            $eventItems = $event->eventItems;

            foreach ($eventItems as $item) {
                $menu = Menu::find($item->menu_id);
                $eventsWithMenus[] = [ //vince orig $ordersWithMenus[] = [
                    'event_id' => $event->id,
                    'customer_name' => $event->customer_first_name . ' ' . $event->customer_last_name,
                    'event_name' => $event->name,
                    'event_room' => $event->room->room_number . ' - ' . $event->room->roomType->name,
                    'start_date' => $event->start_date,
                    'menu_name' => $menu->name,
                    'quantity' => $item->quantity,
                ];
            }
        }
        $title = 'Kitchen Schedule for '.$start_date->format('m/d/y H:i').'-'.$end_date->format('m/d/y H:i');
        if (empty($eventsWithMenus)) {
            return back()->with('error', 'No events with catering found within the specified date range.');
        }
        else {
            return view('admin.kitchen.kitchen-view', compact('eventsWithMenus', 'title'));
        }
    }

   
}
