<?php

namespace App\Http\Controllers;

use App\Models\Guest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class GuestController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $guests = Guest::all();
        return view('admin.guests.view-guests', compact('guests'));
    }

    public function ajaxgetGuests(Request $request)
    {
        $search = $request->query('q');
        $guests = Guest::where('first_name', 'like', "%$search%")
            ->orWhere('last_name', 'like', "%$search%")->orderBy('first_name', 'asc')
            ->get();

        return response()->json($guests);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(Guest $guest)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        $guest = Guest::find($id);
        if ($guest) {
            return view('admin.guests.edit-guests', compact('guest'));
        } else {
            return redirect()->route('admin.guests')->with('error', 'Guest is not found');
        }
    }
   

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $validatedData = $request->validate([
            'first_name' => 'required|string',
            'last_name' => 'required|string',
            'email' => 'nullable|email',
            'phone' => 'required|string',
            'address' => 'nullable|string',
            'notes' => 'nullable|string'
            
        ]);
        $guest = Guest::findOrFail($id);
        // If there are new notes, append them to the existing notes
        //vince added block
        if (!empty($validatedData['notes'])) {
            $username = Auth::user()->name; // Get the logged-in user's name
            $todayDate = Carbon::now()->format('F j, Y'); // Get today's date
            $existingNotes = $guest->notes ? $guest->notes . "\n" : ''; // Add a newline if there are existing notes
            $validatedData['notes'] = $existingNotes . $validatedData['notes']. ' - '.$username.' ('.$todayDate.')';
        }//vince end
        $guest->fill($validatedData);
        $guest->save();
        return redirect()->route('admin.guest.edit', $guest->id)->with('success', 'Guest updated successfully');
        //vince orig return redirect()->route('admin.guests')->with('success', 'Guest updated successfully');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Guest $guest)
    {
        return redirect()->route('admin.guests')->with('error', 'Guest delete is disabled');//vince added
    }
}
