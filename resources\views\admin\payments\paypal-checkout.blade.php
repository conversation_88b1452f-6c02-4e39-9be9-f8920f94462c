@include('layout.admin-header')
@php
    use Carbon\Carbon;

    // Convert the dates to Carbon instances
    $checkinDate = Carbon::parse($invoice->booking->checkin_date);
    $checkoutDate = Carbon::parse($invoice->booking->checkout_date);
@endphp

<!doctype html>
<html lang="en">

<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="icon" type="image/x-icon" href="https://assets.edlin.app/favicon/favicon.ico">

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://assets.edlin.app/bootstrap/v5.3/bootstrap.min.css">

    <!-- PayPal SDK -->
    <script src="https://www.paypal.com/sdk/js?client-id={{ config('paypal.client_id') }}&currency={{config('paypal.currency')}}&intent=capture"></script>

    <!-- Page Title -->
    <title>Checkout - Invoice #{{ $invoice->id }}</title>

    <style>
        .table-custom th, .table-custom td {
            text-align: center;
            vertical-align: middle;
        }
        .card-header {
            background-color: #007bff;
            color: white;
        }
        .card-header-secondary {
            background-color: #6c757d;
            color: white;
        }
        .lead {
            font-size: 1.25rem;
        }
    </style>
</head>
@php
    $currencySymbol = config('currency.symbol');
@endphp


<body data-bs-theme="dark" class="bg-light">
    <div class="container py-5">
        <!-- Logo and Header -->
        <div class="text-center mb-5">
            <img src="https://assets.edlin.app/logo/codewithross/logo-symbol-dark.png" height='100' alt="Logo" />
            <h1 class="mt-3">Checkout - Invoice #{{ $invoice->id }}</h1>
        </div>

        <!-- Booking Summary -->
        <div class="card shadow-sm mb-4">
            <div class="card-header text-center">
                <h3>Booking Summary</h3>
            </div>
            <h4>Guest: {{$invoice->booking->guest_first_name .' '. $invoice->booking->guest_last_name}}</h4>
            <div class="card-body">
                <table class="table table-bordered table-custom">
                    <thead>
                        <tr class="bg-light">
                            <th>Details</th>
                            <th>Information</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Room Number</td>
                            <td>{{ $invoice->booking->room->room_number }}</td>
                        </tr>
                        <tr>
                            <td>Room Type</td>
                            <td>{{ $invoice->booking->room->roomType->name }}</td>
                        </tr>
                        <tr>
                            <td>Check-in Date</td>
                            <td>{{ $checkinDate->format('d/m/Y') }}</td>
                        </tr>
                        <tr>
                            <td>Check-out Date</td>
                            <td>{{ $checkoutDate->format('d/m/Y') }}</td>
                        </tr>
                        
                    </tbody>
                </table>
            </div>

        <!-- Charges Summary -->
        <div class="card shadow-sm mb-4">
            <div class="card-header text-center">
                <h3>Base Charges</h3>
            </div>
            <div class="card-body">
                <table class="table table-bordered table-custom">
                    <thead>
                        <tr class="bg-light">
                            <th>Charge</th>
                            <th>Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Room Rate</td>
                            <td>{{ $currencySymbol }}{{ number_format($invoice->booking->room->roomType->rate, 2) }}</td>
                        </tr>
                        <tr>
                            <td>Number of Days</td>
                            <td>
                                @if ($checkinDate && $checkoutDate)
                                    {{ round($checkinDate->diffInDays($checkoutDate)) }}
                                @else
                                    N/A
                                @endif
                            </td>
                        </tr>
                        <tr class="bg-light">
                            <td><strong>Sub Total</strong></td>
                            <td><strong>{{ $currencySymbol }}{{ number_format(($invoice->booking->room->roomType->rate)*(round($checkinDate->diffInDays($checkoutDate))), 2) }}</strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Additional Charges -->
        <div class="card shadow-sm mb-4">
            <div class="card-header card-header-secondary text-center">
                <h3>Additional Charges</h3>
            </div>
            <div class="card-body">
                <table class="table table-bordered table-custom">
                    <thead>
                        <tr class="bg-light">
                            <th>Charge</th>
                            <th>Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($invoice->additionalCharges as $additionalCharge)
                            <tr>
                                <td>{{ $additionalCharge->description }}</td>
                                <td>{{ $currencySymbol }}{{ number_format($additionalCharge->amount, 2) }}</td>
                            </tr>
                        @endforeach
                        <tr>
                            @php
                                $paypalFeePercentage = get_setting('paypal_service_fee', 0);
                                $paypalFee = $invoice->total_amount * ($paypalFeePercentage / 100);
                            @endphp
                            <td>Paypal Service Fee ({{$paypalFeePercentage}}%)</td>
                            {{-- Get total and multiply by fee percentage --}}
                            <td>{{ $currencySymbol }}{{ number_format($paypalFee, 2) }}</td>
                        </tr>
                        <tr class="bg-light">
                            <td><strong>Total</strong></td>
                            {{-- Add the paypal service fee to the total --}}
                            @php
                                $finalTotal = ceil($invoice->total_amount + $paypalFee);
                            @endphp
                            <td><strong>{{ $currencySymbol }}{{ number_format($finalTotal, 2) }}</strong></td>
                        </tr>
                        
                    </tbody>
                </table>
            </div>
        </div>

        <!-- PayPal and Payment Options -->
        <div class="card text-center mb-4">
            <div class="card-header">
                <h3>Payment Options</h3>
            </div>
            <p class="lead mb-4">Click the button below to proceed with payment.</p>
            <div id="paypal-success" class="alert alert-success" role="alert" style="display: none;">
                Payment Successful! Thank you!
            </div>


                
            
            <div class="input-group mb-4 w-50 mx-auto">
                <span class="input-group-text">{{ $currencySymbol }}</span>
                <input type="text" class="form-control" id="paypal-amount" value="{{$finalTotal}}" aria-label="Amount" readonly>
            </div>

            

            
            {{-- Container for Payment Options Dynamically added --}}
            <div class="input-group mb-4 w-50 mx-auto"  id="payment_options"></div>
            </div>
        </div>

      

    <!-- PayPal Script -->
    <script>
        paypal.Buttons({
            createOrder: function() {
                const invoiceId = "{{ $invoice->id }}";
                return fetch("/admin/payments/paypal/create/" + document.getElementById("paypal-amount").value)
                    .then((response) => response.text())
                    .then((id) => {
                        return id;
                    });
            },
            onApprove: function() {
                return fetch("/admin/payments/paypal/complete", {
                    method: "post",
                    headers: {
                        "X-CSRF-Token": '{{ csrf_token() }}',
                        "Content-Type": "application/json"
                    },
                  body: JSON.stringify({invoice_id: "{{ $invoice->id }}", amount: document.getElementById("paypal-amount").value})
                })
                .then((response) => response.json())
                .then((order_details) => {
                    console.log(order_details);
                    document.getElementById("paypal-success").style.display = 'block';
                    document.getElementById("payment_options").style.display = 'none';

                    // Redirect to the invoice page after 3 seconds
                    setTimeout(() => {
                        window.location.href = "/admin/invoices";
                    }, 3000);
                })
                .catch((error) => {
                    console.log(error);
                });
            },
            onCancel: function(data) {
                console.log("Payment cancelled.");
            },
            onError: function(err) {
                console.log(err);
            }
        }).render('#payment_options');
    </script>
</body>

</html>
