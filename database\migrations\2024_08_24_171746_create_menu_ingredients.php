<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('menu_ingredients', function (Blueprint $table) {
            $table->id();
            $table->foreignId('menu_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('ingredient_id')->nullable()->constrained()->onDelete('cascade');
            $table->decimal('quantity', 8, 2)->nullable();//vince orig $table->float('quantity')->nullable();
            $table->string('unit')->nullable();
            $table->timestamps();
        });

         // Insert some data
         DB::table('menu_ingredients')->insert([
            [
                'menu_id' => 1,
                'ingredient_id' => 1,
                'quantity' => 10, // Example quantity
                'unit' => 'grams', // Example unit
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'menu_id' => 1,
                'ingredient_id' => 2,
                'quantity' => 10, // Example quantity
                'unit' => 'grams', // Example unit
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'menu_id' => 1,
                'ingredient_id' => 3,
                'quantity' => 10, // Example quantity
                'unit' => 'grams', // Example unit
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'menu_id' => 2,
                'ingredient_id' => 3,
                'quantity' => 10, // Example quantity
                'unit' => 'grams', // Example unit
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'menu_id' => 3,
                'ingredient_id' => 3,
                'quantity' => 10, // Example quantity
                'unit' => 'grams', // Example unit
                'created_at' => now(),
                'updated_at' => now(),
            ]
            // Add more entries as needed for other menu items and their ingredients
        ]);

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('menu_ingredients');
    }
};
