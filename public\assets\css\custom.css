@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap');



body {
    min-height: 100vh;
}
/* Running Text */

    #runningText {
        background: #283971;
        color: #fff;
        padding: 10px;
        font-size: 20px;
        white-space: nowrap;
        overflow: hidden;
        box-sizing: border-box;
    }

    #runningText div {
        display: inline-block;
        padding-left: 100%;
        animation: marquee 15s linear infinite;
    }

    @keyframes marquee {
        0% {
            transform: translate(0, 0);
        }

        100% {
            transform: translate(-100%, 0);
        }
    }



/* Search Bar */
.custom-search {
    border-radius: 20px; /* Adjust the value as needed */
    color: #001573;
    font-family: 'Roboto', sans-serif;
    height: 40px;
    width: 600px;
    
}

.custombg-image-row {
    background-image: url('../images/BG.png'); /* Specify the path to your image */
    background-size: cover; /* Cover the entire area of the row */
    background-position: center; /* Center the background image */
    background-repeat: no-repeat; /* Do not repeat the image */
    height: 50vw;
    object-fit: contain;
}

.customPic {
    width: 100%;
    height: 11vw;
    object-fit: contain;

}


.mainContent {
    display: flex;
    margin: 100px 0;
    justify-content: center;
}



/* Gallery CSS*/



.projectTitle {
    background-color: #A19158;
   
    position: absolute;
    color: white;
    font-size: 17px;
    font-weight: bold;
    text-align: center;
    padding: 20px 0;
    top: 0;
    width: 80%;
    left: 0;
    
    border-top-left-radius: 30px;
    border-bottom-right-radius: 20px;
}

.galleryImage {
    border-top-left-radius: 15%;

}
.gallery{
    background-color: #A19158;
    z-index: 100;
    position:absolute;
    color: white;
    font-size: 34px;
    font-family: "Inter", sans-serif;
    font-optical-sizing: auto;
    font-weight: 800;
    font-style: normal;
    font-variation-settings: "slnt" 0;
    text-indent: 5rem;
    padding: 15px 0;
    margin-top: 20px;
}

/* Gallery CSS*/

.row {
    display: flex;
    justify-content: center;
}

.customHome{
    background-color: #283971;
    color:aliceblue;
    border-radius: 20px;
    font-weight: bold;   
    z-index: 100;
    
}

.customNavbar{
    background-color: #283971;
    color:aliceblue;
    white-space: nowrap;
}

.customSidebar{
    background-color: #283971;
}


.footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 80px; /* Height of the footer */
    background-color: #f5f5f5;
}


.XULOGO{
    width: 10px;
    height: 10px;

}

.customfont{
    font-family: "Inter", sans-serif;
    font-optical-sizing: auto;
    font-weight: 700;
    font-style: normal;
    font-variation-settings: "slnt" 0;
    color:#283971;
    border-color: aqua;
}

.footerText {
    font-family: "Inter", sans-serif;
    font-optical-sizing: auto;
    font-weight: 400;
    font-style: normal;
    font-variation-settings: "slnt" 0;
    
}

.customSidebar{
    width: 60%;
}

.nav-link {
    right: 0; /* Adjust the value as needed for spacing */
}
.btn_custombtns {
    font-size: 30px;
}


/* index / home */
.headImage{

    display: flex;
    text-align: center;
    align-items: center;
    
}

.headPic {
    position: relative;
    object-fit: contain;
    width: 60vw;
    border-radius: 20px;
    margin-bottom: 1rem;
   
   
}

.articles{
    background-color: #A19158;
    z-index: 100;
    position:absolute;
    color: white;
    font-size: 34px;
    font-family: "Inter", sans-serif;
    font-optical-sizing: auto;
    font-weight: 800;
    font-style: normal;
    font-variation-settings: "slnt" 0;
    text-indent: 1cqw;
    padding: 15px 0;
    margin-top: -70px;
}

.articleImage{
    margin-top: 1rem;
    width: 100%;
    height: 30vh;

}


.secondContent {
    display: flex;
    flex-direction: row;
    justify-content: center;
    margin-top: -3rem;
}
.FmarginCard {
    margin-top: 3rem;
}

.marginCard {
    margin-top: 10rem;
}


.projects{
    
    background-color: #A19158;
    z-index: 100;
    position: absolute;
    color: white;
    font-size: 34px;
    font-family: "Inter", sans-serif;
    font-optical-sizing: auto;
    font-weight: 800;
    font-style: normal;
    font-variation-settings: "slnt" 0;
    padding: 15px 0;
    margin-top: 50px;
    text-indent: 2cqw;
   
    
}

/*index*/


/*article*/
.articlePage {
    
    background-color: white;
 
    position: absolute;
    color: #283971;
    font-size: 48px;
    font-family: "Inter", sans-serif;
    font-optical-sizing: auto;
    font-weight: 800;
    font-style: normal;
    font-variation-settings: "slnt" 0;
    padding: 30px 0;
   
    text-align: center;
}

.articleLine {
    margin-top: 50px;
    z-index: 100;
    height: 50px;
    border-bottom: 3px solid #283971;
    width: 50%;
    margin-bottom: 0px;
    
}
/*article*/

/*partners*/
