<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\BelongsToHotel;

class ActivityLog extends Model
{
    use HasFactory, BelongsToHotel;

    protected $table = 'activity_logs';
    protected $fillable = ['hotel_id', 'description', 'user_email', 'module'];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_email', 'email');
    }
}
