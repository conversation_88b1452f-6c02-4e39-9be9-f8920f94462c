<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('roles', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->boolean('is_system_role')->default(false); // Cannot be deleted
            $table->json('permissions');
            $table->timestamps();
        });

        Schema::create('hotel_user_roles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('hotel_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('role_id')->constrained()->onDelete('cascade');
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->unique(['hotel_id', 'user_id', 'role_id']);
        });

        // Create default roles
        $defaultRoles = [
            [
                'name' => 'Super Admin',
                'slug' => 'super-admin',
                'description' => 'Full system access across all hotels',
                'is_system_role' => true,
                'permissions' => json_encode([
                    'hotels.manage',
                    'users.manage',
                    'subscriptions.manage',
                    'billing.manage',
                    'system.settings',
                    'reports.all',
                    '*' // Wildcard for all permissions
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Hotel Admin',
                'slug' => 'hotel-admin',
                'description' => 'Full access to hotel management',
                'is_system_role' => true,
                'permissions' => json_encode([
                    'hotel.manage',
                    'users.manage',
                    'rooms.manage',
                    'bookings.manage',
                    'guests.manage',
                    'payments.manage',
                    'reports.view',
                    'settings.manage',
                    'restaurant.manage',
                    'events.manage',
                    'branding.manage'
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Manager',
                'slug' => 'manager',
                'description' => 'Hotel operations management',
                'is_system_role' => true,
                'permissions' => json_encode([
                    'rooms.manage',
                    'bookings.manage',
                    'guests.manage',
                    'payments.view',
                    'reports.view',
                    'restaurant.manage',
                    'events.manage'
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Front Desk',
                'slug' => 'front-desk',
                'description' => 'Front desk operations',
                'is_system_role' => true,
                'permissions' => json_encode([
                    'bookings.manage',
                    'guests.manage',
                    'rooms.view',
                    'payments.create',
                    'checkin.manage',
                    'checkout.manage'
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Housekeeping',
                'slug' => 'housekeeping',
                'description' => 'Room maintenance and cleaning',
                'is_system_role' => true,
                'permissions' => json_encode([
                    'rooms.view',
                    'rooms.update_status',
                    'maintenance.manage'
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Restaurant Staff',
                'slug' => 'restaurant-staff',
                'description' => 'Restaurant and kitchen operations',
                'is_system_role' => true,
                'permissions' => json_encode([
                    'restaurant.manage',
                    'orders.manage',
                    'menu.view',
                    'ingredients.view'
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Accountant',
                'slug' => 'accountant',
                'description' => 'Financial management and reporting',
                'is_system_role' => true,
                'permissions' => json_encode([
                    'payments.view',
                    'invoices.manage',
                    'reports.financial',
                    'expenses.manage',
                    'remittances.manage'
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Guest',
                'slug' => 'guest',
                'description' => 'Limited guest access',
                'is_system_role' => true,
                'permissions' => json_encode([
                    'bookings.view_own',
                    'profile.manage'
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ]
        ];

        DB::table('roles')->insert($defaultRoles);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('hotel_user_roles');
        Schema::dropIfExists('roles');
    }
};
