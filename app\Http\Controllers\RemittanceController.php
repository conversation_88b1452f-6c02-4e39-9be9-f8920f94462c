<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Remittance;
use App\Models\Payment;
use App\Models\User;
use App\Models\Remittance_Payment;
use App\Models\Remittance_Document;

class RemittanceController extends Controller
{
    public function index()
    {
        $users = User::all();
        $all_remittances = Remittance::all();
        
        //remittances for remittances with matching to_user_email
        $pending_remittances = Remittance::with('payment')->where('to_user_email', get_user_email())->get();
        return view('admin.remittances.view-remittances', compact('all_remittances','pending_remittances', 'users'));
    }

    public function add()
    {
        return view('admin.remittances.add-remittances');
    }

    public function confirm($remittance_id)
    {
        $remittance = Remittance::findOrFail($remittance_id);
        $remittance_payments = Remittance_Payment::where('remittance_id', $remittance_id)->get();
        $payments = Payment::whereIn('id', json_decode($remittance->payment_id))->get();

        //Update remittance confirmed_by and status
        $remittance->location = 'with ' . get_user_email();
        $remittance->confirmed_by = get_user_email();
        $remittance->status = 'confirmed';
        $remittance->save();

        //Update remittance_payments confirmed_by and status
        foreach ($remittance_payments as $remittance_payment) {
            $remittance_payment->location = 'with ' . get_user_email();
            $remittance_payment->confirmed_by = get_user_email();
            $remittance_payment->status = 'confirmed';
            $remittance_payment->save();
        }

        //Update payments is_remitted status and current_location
        foreach ($payments as $payment) {
            $payment->is_remitted = 'yes';
            $payment->current_location = 'with ' . get_user_email();
            $payment->status = 'completed';
            $payment->confirmed_by = get_user_email();
            $payment->save();
        }

        //Log the activity
        log_activity('Remittance confirmed', 'Remittance Module');

        return redirect()->route('admin.remittances')->with('success', 'Remittance confirmed successfully.');
    }


    // Bank remittance page
    public function bank(Request $request)
    {
        // Retrieve and split the query parameters
        $remittanceIds = explode(',', $request->query('remittances', ''));
        $fromUserEmail = $request->query('from_user_email');
        $toUserEmail = $request->query('to_user_email');

        // Find the remittances using the IDs
        $remittances = Remittance::whereIn('id', $remittanceIds)->get();

        if ($remittances->isEmpty()) {
            return redirect()->back()->with('error', 'No remittances found.');
        }

        // Collect all payment IDs from the remittances
        $paymentIds = $remittances->flatMap(function ($remittance) {
            return json_decode($remittance->payment_id);
        })->unique();

        // Retrieve the payments
        $payments = Payment::whereIn('id', $paymentIds)->get();

        // Pass data to the view
        return view('admin.remittances.bank-remittance-upload', [
            'remittances' => $remittances,
            'payments' => $payments,
            'from_user_email' => $fromUserEmail,
            'to_user_email' => $toUserEmail
        ]);
    }

    // Upload bank remittance documents
    public function remittance_documents_upload(Request $request)
    {
        $request->validate([
            'remittance_ids' => 'required|array',
            'remittance_ids.*' => 'exists:remittances,id',
            'documents' => 'required|array',
            'documents.*' => 'file|mimes:pdf,jpg,jpeg,png|max:2048'
        ]);

        foreach ($request->input('remittance_ids') as $remittanceId) {
            foreach ($request->file('documents') as $document) {
                // Generate a custom file name
                $originalName = pathinfo($document->getClientOriginalName(), PATHINFO_FILENAME); // Get the original file name without extension
                $extension = $document->getClientOriginalExtension(); // Get the file extension
                $timestamp = now()->format('YmdHis'); // Generate a timestamp
                $fileName = "{$originalName}_{$timestamp}.{$extension}"; // Combine into a new file name

                // Store the document with the new file name
                $path = $document->storeAs('uploads/bank_remittance_documents', $fileName);

                // Save the document information to the database
                Remittance_Document::create([
                    'remittance_id' => $remittanceId,
                    'file_name' => $fileName,
                    'file_type' => $document->getClientMimeType(),
                    'file_size' => $document->getSize(),
                    'file_path' => $path
                ]);
            }

            //update remittance status
            Remittance::where('id', $remittanceId)->update(['status' => 'deposited to bank']);
        }

        //Update payments current location
        $remittances = Remittance::whereIn('id', $request->input('remittance_ids'))->get();
        foreach ($remittances as $remittance) {
            $payments = Payment::whereIn('id', json_decode($remittance->payment_id))->get();
            foreach ($payments as $payment) {
                $payment->current_location = 'deposited to bank';
                $payment->save();
            }
        }

        //Update remittance status
        Remittance::whereIn('id', $request->input('remittance_ids'))->update(['status' => 'completed']);

        //Update remittance_payments status
        $remittance_payments = Remittance_Payment::whereIn('remittance_id', $request->input('remittance_ids'))->get();
        foreach ($remittance_payments as $remittance_payment) {
            $remittance_payment->status = 'completed';
            $remittance_payment->location = 'deposited to bank';
            $remittance_payment->save();
        }

        // Log the activity
        log_activity('Bank remittance documents uploaded', 'Remittance Module');
        return redirect()->back()->with('success', 'Documents uploaded successfully.');
    }

    // Upload bank remittance documents
    public function remittance_documents_upload_single(Request $request)
    {
        $request->validate([
            'remittance_id' => 'required|exists:remittances,id',
            'documents' => 'required|array',
            'documents.*' => 'file|mimes:pdf,jpg,jpeg,png|max:2048'
        ]);

            foreach ($request->file('documents') as $document) {
                // Generate a custom file name
                $originalName = pathinfo($document->getClientOriginalName(), PATHINFO_FILENAME); // Get the original file name without extension
                $extension = $document->getClientOriginalExtension(); // Get the file extension
                $timestamp = now()->format('YmdHis'); // Generate a timestamp
                $fileName = "{$originalName}_{$timestamp}.{$extension}"; // Combine into a new file name

                // Store the document with the new file name
                $path = $document->storeAs('uploads/bank_remittance_documents', $fileName);

                // Save the document information to the database
                Remittance_Document::create([
                    'remittance_id' => $request->input('remittance_id'),
                    'file_name' => $fileName,
                    'file_type' => $document->getClientMimeType(),
                    'file_size' => $document->getSize(),
                    'file_path' => $path
                ]);
            }
        //update remittance status
        Remittance::where('id', $request->input('remittance_id'))->update(['status' => 'deposited to bank']);

        //Update payments current location
        $remittance = Remittance::findOrFail($request->input('remittance_id'));
        $payments = Payment::whereIn('id', json_decode($remittance->payment_id))->get();
        foreach ($payments as $payment) {
            $payment->current_location = 'deposited to bank';
            $payment->save();
        }

        //Update remittance status
        $remittance->status = 'completed';

        //Update remittance_payments status
        $remittance_payments = Remittance_Payment::where('remittance_id', $request->input('remittance_id'))->get();
        foreach ($remittance_payments as $remittance_payment) {
            $remittance_payment->status = 'completed';
            $remittance_payment->location = 'deposited to bank';
            $remittance_payment->save();
        }
        // Log the activity
        log_activity('Bank remittance documents uploaded', 'Remittance Module');

        return redirect()->back()->with('success', 'Documents uploaded successfully.');
    }




    // Bank remittance page for a single remittance
    public function bank_single($remittance_id)
    {
        $remittance = Remittance::with('payment')->findOrFail($remittance_id);
        $remittance_payments = Remittance_Payment::where('remittance_id', $remittance_id)->get();
        $payments = Payment::whereIn('id', json_decode($remittance->payment_id))->get();

        return view('admin.remittances.bank-remittance-upload-single', compact('remittance', 'remittance_payments', 'payments'));
    }


    public function transfer(Request $request)
    {
        $request->validate([
            'remittances' => 'required|array',
            'remittances.*' => 'exists:remittances,id',
            'from_user_email' => 'required',
            'to_user_email' => 'required',
            'amount' => 'nullable|numeric',
        ]);

        // Check if the destination is the bank
        if ($request->input('to_user_email') == 'bank') {
            $remittanceIds = $request->input('remittances');
            // Redirect to the bank remittance page with each remittance ID
            return response()->json([
                'success' => true,
                'redirect' => route('admin.remittances.bank', $remittanceIds[0]) // Redirect to the first remittance ID
            ]);
        }

        $remittances = Remittance_Payment::whereIn('id', $request->input('remittances'))->get();

        //For Each Remittances selected, update remittances_payment table where remittance_id is in the array of remittances selected
        foreach ($remittances as $remittance) {
            $remittance->from_user_email = $request->input('from_user_email');
            $remittance->to_user_email = $request->input('to_user_email');
            $remittance->amount = $request->input('amount');
            $remittance->location = 'with ' . $request->input('from_user_email');
            $remittance->remittance_date = now();
            $remittance->confirmed_by = null;
            $remittance->status = 'pending';
            $remittance->save();

            // Update the payment's is_remitted status
            $payment = Payment::find($remittance->payment_id);
            $payment->is_remitted = 'waiting for confirmation from ' . $request->input('to_user_email');
            $payment->current_location = 'with ' . $request->input('from_user_email');
        }

        // Log the activity
        log_activity('Remittance transfer initiated', 'Remittance Module');

        return response()->json(['success' => true]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'payments' => 'required|array',
            'payments.*' => 'exists:payments,id', // Validate each payment ID
            'from_user_email' => 'required',
            'to_user_email' => 'required',
            'amount' => 'required|numeric',
        ]);

        //if to_user_email == bank, redirect to bank remittance blade file
        if ($request->input('to_user_email') == 'bank') {
            return redirect()->route('admin.remittances.bank', ['payments' => $request->input('payments'), 'from_user_email' => $request->input('from_user_email'), 'amount' => $request->input('amount')]);
        }

        $paymentIds = $request->input('payments');
        $fromUserEmail = $request->input('from_user_email');
        $toUserEmail = $request->input('to_user_email');
        $amount = $request->input('amount');

        $array_of_payment_ids = [];
        foreach ($paymentIds as $paymentId) {
            $payment = Payment::find($paymentId);

            if ($payment && $payment->is_remitted == 'no') {
                $array_of_payment_ids[] = $paymentId;
            }
        }

        // Create a new remittance
        Remittance::create([
            'payment_id' =>  json_encode($array_of_payment_ids), //Store array of payment ids
            'from_user_email' => $fromUserEmail,
            'to_user_email' => $toUserEmail,
            'amount' => $amount,
            'location' => 'with ' . $fromUserEmail,
            'remittance_date' => now(),
            'status' => 'pending'
        ]);

        //get the last inserted remittance id
        $remittance_id = Remittance::latest()->first()->id;


        foreach ($paymentIds as $paymentId) {
            $payment = Payment::find($paymentId);
            //Find the remittance with relationship
            $remittance = Remittance::with('payment')->find($remittance_id);

            if ($payment && $payment->is_remitted == 'no') {

                // Create a new remittance_payment
                Remittance_Payment::create([
                    'payment_id' => $paymentId,
                    'remittance_id' => $remittance_id,
                    'from_user_email' => $fromUserEmail,
                    'to_user_email' => $toUserEmail,
                    'amount' => $payment->amount,
                    'location' => 'with ' . $fromUserEmail,
                    'remittance_date' => now(),
                    'confirmed_by' => null,
                    'status' => 'pending'
                ]);


                // Update the payment's is_remitted status
                $payment->is_remitted = 'waiting for confirmation from ' . $toUserEmail;
                $payment->current_location = 'with ' . $fromUserEmail;
                $payment->save();
            }
        }


        // Log the activity
        log_activity('Remittance Stored', 'Remittance Module');

        return response()->json(['success' => true]);
    }



    public function edit($id)
    {
        $remittance = Remittance::findOrFail($id);
        return view('remittance.edit', compact('remittance'));
    }

    public function update(Request $request, $id)
    {

        return redirect()->route('remittance.index')->with('success', 'Remittance updated successfully.');
    }

    public function destroy($id)
    {
        $remittance = Remittance::findOrFail($id);
        $remittance->delete();

        return redirect()->route('remittance.index')->with('success', 'Remittance deleted successfully.');
    }
}
