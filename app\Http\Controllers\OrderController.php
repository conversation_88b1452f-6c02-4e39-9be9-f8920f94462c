<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Http\Request;
use App\Models\Menu;
use App\Models\Ingredient;
use App\Models\Room;
use App\Models\Invoice;
use App\Models\AdditionalCharge;
use App\Models\Payment;
use App\Models\Booking; // <-- Make sure this line is included
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;//vince added

class OrderController extends Controller
{
    public function index()
    {
        $orders = Order::with('orderItems')->get();
        $title = 'Restaurant Orders';//vince added
        return view('admin.orders.view-orders', compact('orders', 'title'));
    }

    public function add()
    {
        $title = 'POS - New Order';
        $menu_list = Menu::orderBy('name', 'asc')->get();//vince orig Menu::all();
        return view('admin.orders.add-orders', compact('menu_list', 'title'));
    }

   public function store(Request $request)
    {
        // Decode the JSON data from the request
        $orderData = json_decode($request->input('orderData'), true);

        // Validate the data
        $validated = Validator::make($orderData, [
            'table_name' => 'nullable|string',//vince orig order_number
            'customer_name' => 'nullable|string',
            'items' => 'required|array',
            'items.*.menu_id' => 'required|exists:menus,id',
            'items.*.quantity' => 'required|integer|min:1',
            'total_amount' => 'required|numeric',
            'received_amount' => 'required|numeric',
            'change_amount' => 'required|numeric',
            'room_charge' => 'boolean', // Ensures room_charge is a boolean
            'room_number' => 'nullable|exists:rooms,id'
        ])->validate();

        // Create the order
        $order = Order::create([
            'table_name' => $validated['table_name'],//vince orig order_number
            'customer_name' => $validated['customer_name'],
            'total_amount' => $validated['total_amount'],
            'received_amount' => $validated['received_amount'],
            'change_amount' => $validated['change_amount'],
            'room_charge' => $validated['room_charge'],
            'room_id' => $validated['room_charge'] ? $validated['room_number'] : null
        ]);

        // Loop through items and add them to the order
        $totalPrice = 0;
        foreach ($validated['items'] as $item) {
            $menu = Menu::find($item['menu_id']);
            $orderItem = OrderItem::create([
                'order_id' => $order->id,
                'menu_id' => $menu->id,
                'quantity' => $item['quantity'],
                'price' => $menu->price //vince orig $menu->price * $item['quantity'],
            ]);
            $totalPrice += $orderItem->price * $orderItem->quantity;//vince orig $orderItem->price

            // Update stock based on ingredients used
            //vince $this->updateIngredientStock($orderItem);
        }

        // Update total price of the order
        $order->update(['total_price' => $totalPrice]);

        // Check if the order is charged to the room, else create a paid invoice and payment
        if ($validated['room_charge']) {
            // Find the booking associated with the room
            $booking = Booking::where('room_id', $validated['room_number'])->first();
            
            // Find or create an invoice for the booking if it exists
            $invoice = null;
            if ($booking) {
                $invoice = Invoice::where('booking_id', $booking->id)
                    ->where('status', 'unpaid') // Check for unpaid invoices
                    ->first();
            }

            if (!$invoice) {
                // Create a new invoice if none exists
                $invoice = Invoice::create([
                    'booking_id' => $booking->id, // Link to booking
                    'total_amount' => 0, // Initial total amount
                    'invoice_date' => now(), // Set the invoice date
                    'status' => 'unpaid', // Set status to unpaid
                    'created_by' => Auth::user()->name,
                    'updated_by' => Auth::user()->name //vince added
                ]);
            }

            // Add the order as an additional charge
            AdditionalCharge::create([
                'invoice_id' => $invoice->id,
                'order_id' => $order->id,//vince added
                'description' => 'POS Order Charge to Room',
                'amount' => $totalPrice,
            ]);

            // Update invoice total amount
            $invoice->update(['total_amount' => $invoice->total_amount + $totalPrice]);
            
            //Update Order Room Charge Boolean
            $order->update(['room_charge' => true]);
            
            // Update the order status to pending because will be paid when they checkout
            $order->update(['order_status' => 'pending']);

            
        } else {
            // Handle the case for non-room charge
            $invoice = Invoice::create([
                'booking_id' => null,
                'total_amount' => $totalPrice,
                'invoice_date' => now(),
                'status' => 'paid',
                'created_by' => Auth::user()->name,
                'updated_by' => Auth::user()->name //vince added
            ]);
            $order->update(['invoice_id' => $invoice->id]);//vince added
            // Create a payment for the invoice
            Payment::create([
                'invoice_id' => $invoice->id,
                'amount' => $totalPrice,
                'payment_date' => now(),
                'payment_method' => 'cash',
                'is_remitted' => 'no',
                'current_location' => Auth::user()->name,
                'created_by' => Auth::user()->name,
            ]);
            
            // Update the order status to paid
        $order->update(['order_status' => 'paid']);
        }

        return response()->json($order);
    }



    


    private function generateOrderNumber()
    {
        return strtoupper(uniqid('ORD'));
    }

    private function updateIngredientStock(OrderItem $orderItem)
    {
        // Logic to increase pending usage of ingredients based on order item quantity
        $orderItem->menu->ingredients->each(function ($ingredient) use ($orderItem) {
            $ingredient->update([
                'pending_usage' => $ingredient->pending_usage + ($ingredient->pivot->quantity * $orderItem->quantity)
            ]);
        });
    }
    //vince added function
    public function edit($id)
    { 
        $order = Order::find($id);
        if ($order){
            $title = 'POS - Edit Order';
            $menu_list = Menu::orderBy('name', 'asc')->get();//vince orig Menu::all();
            return view('admin.orders.edit-orders', compact('menu_list', 'order', 'title'));
        } else {
            return redirect()->route('admin.orders')->with('error', 'Order is not found');
        }
    }

    public function update(Request $request, $id)
    {
        // Decode the JSON data from the request
        $orderData = json_decode($request->input('orderData'), true);

        // Validate the data
        $validated = Validator::make($orderData, [
            'table_name' => 'nullable|string',//vince orig order_number
            'customer_name' => 'nullable|string',
            'items' => 'required|array',
            'items.*.menu_id' => 'required|exists:menus,id',
            'items.*.quantity' => 'required|integer|min:1',
            'total_amount' => 'required|numeric',
            'received_amount' => 'required|numeric',
            'change_amount' => 'required|numeric',
            'room_charge' => 'boolean', // Ensures room_charge is a boolean
            'room_number' => 'nullable|exists:rooms,id'
        ])->validate();

        // Update the order
        $order = Order::findOrFail($id);
        $order->fill([
            'table_name' => $validated['table_name'],//vince orig order_number
            'customer_name' => $validated['customer_name'],
            'total_amount' => $validated['total_amount'],
            'received_amount' => $validated['received_amount'],
            'change_amount' => $validated['change_amount'],
            'room_charge' => $validated['room_charge'],
            'room_id' => $validated['room_charge'] ? $validated['room_number'] : null
        ]);
        $order->save();
        $order->orderItems()->delete();
        // Loop through items and add them to the order
        $totalPrice = 0;
        foreach ($validated['items'] as $item) {
            $menu = Menu::find($item['menu_id']);
            $orderItem = OrderItem::create([
                'order_id' => $order->id,
                'menu_id' => $menu->id,
                'quantity' => $item['quantity'],
                'price' => $menu->price//vince orig $menu->price * $item['quantity'],
            ]);
            $totalPrice += $orderItem->price * $orderItem->quantity;//vince orig $orderItem->price

            // Update stock based on ingredients used
            //vince $this->updateIngredientStock($orderItem);
        }

        // Update total price of the order
        $order->update(['total_price' => $totalPrice]);
        $additionalCharge = AdditionalCharge::where('order_id', $order->id)->first();//vince added
        if ($additionalCharge) {//vince added
            $additionalCharge->delete();
        }
        // Check if the order is charged to the room, else create a paid invoice and payment
        if ($validated['room_charge']) {
            // Find the booking associated with the room
            $booking = Booking::where('room_id', $validated['room_number'])->first();
            
            // Find or create an invoice for the booking if it exists
            $invoice = null;
            if ($booking) {
                $invoice = Invoice::where('booking_id', $booking->id)
                    ->where('status', 'unpaid') // Check for unpaid invoices
                    ->first();
            }

            if (!$invoice) {
                // Create a new invoice if none exists
                $invoice = Invoice::create([
                    'booking_id' => $booking->id, // Link to booking
                    'total_amount' => 0, // Initial total amount
                    'invoice_date' => now(), // Set the invoice date
                    'status' => 'unpaid', // Set status to unpaid
                    'created_by' => Auth::user()->name,
                    'updated_by' => Auth::user()->name //vince added
                ]);
            }

            // Add the order as an additional charge
            $additionalCharge = AdditionalCharge::create([
                    'invoice_id' => $invoice->id,
                    'order_id' => $order->id,
                    'description' => 'POS Order Charge to Room',
                    'amount' => $totalPrice,
                ]);

            // Update invoice total amount
            $invoice->update(['total_amount' => $invoice->total_amount + $totalPrice]);
            
            //Update Order Room Charge Boolean
            $order->update(['room_charge' => true]);
            
            // Update the order status to pending because will be paid when they checkout
            $order->update(['order_status' => 'pending']);

            
        } else {
            // Handle the case for non-room charge
            $invoice = Invoice::findOrFail($order->invoice_id);
            $invoice->fill([
                'booking_id' => null,
                'total_amount' => $totalPrice,
                'invoice_date' => now(),
                'status' => 'paid',
                'updated_by' => Auth::user()->name //vince orig 'created_by' => Auth::user()->name
            ]);
            $invoice->save();
            // Create a payment for the invoice
            $payment = Payment::where('invoice_id', $order->invoice_id)->firstOrFail();
            $payment->fill([
                'invoice_id' => $invoice->id,
                'amount' => $totalPrice,
                'payment_date' => now(),
                'payment_method' => 'cash',
                'is_remitted' => 'no',
                'current_location' => Auth::user()->name,
                'created_by' => Auth::user()->name
            ]);
            $payment->save();   
            
            // Update the order status to paid
            $order->update(['order_status' => 'paid']);
        }
        return response()->json($order);
    }

    //vince added function
    public function destroy(Order $order)
    {
        return redirect()->route('admin.orders')->with('error', 'Order delete is disabled');//vince added
    }
}
