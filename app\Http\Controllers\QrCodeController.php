<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use App\Models\Invoice;
use App\Models\Booking;

class QrCodeController extends Controller
{
    public function generateQrCode($type, $id)
    {
        $url = $this->getPaymentUrl($type, $id);
        return QrCode::size(400)->generate($url);
    }

    private function getPaymentUrl($type, $id)
    {
        switch ($type) {
            case 'paypal':
                return route('admin.payments.paypal', ['invoice_id' => $id]);
            case 'paymongo':
                return route('admin.payments.paymongo', ['invoice_id' => $id]);
            case 'guest-portal-payment':
                // Generate guest portal payment link
                $invoice = Invoice::with('hotel')->findOrFail($id);
                return route('guest-portal.payment', [$invoice->hotel->slug, $invoice->id]);
            case 'guest-portal-invoice':
                // Generate guest portal invoice link
                $invoice = Invoice::with('hotel')->findOrFail($id);
                return route('guest-portal.invoice', [$invoice->hotel->slug, $invoice->id]);
            case 'guest-portal-booking':
                // Generate guest portal booking link
                $booking = Booking::with('hotel')->findOrFail($id);
                return route('guest-portal.hotel', $booking->hotel->slug) . '?booking=' . $booking->id;
            default:
                return '#';
        }
    }

    /**
     * Generate QR code for guest portal payment
     */
    public function generateGuestPaymentQR($invoiceId)
    {
        $invoice = Invoice::with('hotel')->findOrFail($invoiceId);
        $url = route('guest-portal.payment', [$invoice->hotel->slug, $invoice->id]);

        return QrCode::format('png')
            ->size(300)
            ->margin(2)
            ->generate($url);
    }

    /**
     * Generate QR code for guest portal invoice access
     */
    public function generateGuestInvoiceQR($invoiceId)
    {
        $invoice = Invoice::with('hotel')->findOrFail($invoiceId);
        $url = route('guest-portal.invoice', [$invoice->hotel->slug, $invoice->id]);

        return QrCode::format('png')
            ->size(300)
            ->margin(2)
            ->generate($url);
    }

    /**
     * Generate QR code for hotel portal access
     */
    public function generateHotelPortalQR($hotelSlug)
    {
        $url = route('guest-portal.hotel', $hotelSlug);

        return QrCode::format('png')
            ->size(300)
            ->margin(2)
            ->generate($url);
    }
}
