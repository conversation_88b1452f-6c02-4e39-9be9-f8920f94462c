<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\BelongsToHotel;

class Remittance extends Model
{
    use HasFactory, BelongsToHotel;

    protected $table = 'remittances';

    protected $fillable = [
        'hotel_id',
        'payment_id',
        'from_user_email',
        'to_user_email',
        'amount',
        'location',
        'remittance_date',
        'confirmed_by',
        'status',
    ];

    // Relationship with Payment
    public function payment()
    {
        return $this->belongsTo(Payment::class);
    }

    // Relationship with User (for from_user_email)
    public function fromUser()
    {
        return $this->belongsTo(User::class, 'from_user_email', 'email');
    }

    // Relationship with User (for to_user_email)
    public function toUser()
    {
        return $this->belongsTo(User::class, 'to_user_email', 'email');
    }
    
}
