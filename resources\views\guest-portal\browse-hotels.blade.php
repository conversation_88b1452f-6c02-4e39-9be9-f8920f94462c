<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Find Your Perfect Hotel</title>
    
    <!-- Bootstrap 5 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
        }
        
        .hero-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            padding: 4rem 0;
        }
        
        .hotel-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .hotel-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .price-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: var(--primary-color);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-weight: bold;
        }
        
        .search-form {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 2rem;
            margin-top: -3rem;
            position: relative;
            z-index: 10;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            border-color: var(--primary-dark);
        }
        
        .text-primary {
            color: var(--primary-color) !important;
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="display-4 fw-bold mb-4">Find Your Perfect Hotel</h1>
                    <p class="lead mb-0">Discover amazing hotels and book your ideal stay with the best rates guaranteed</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Search Form -->
    <div class="container">
        <div class="row">
            <div class="col-lg-10 mx-auto">
                <div class="search-form">
                    <form id="searchForm">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label for="location" class="form-label">
                                    <i class="fas fa-map-marker-alt me-1"></i>Location
                                </label>
                                <select class="form-select" id="location" name="location">
                                    <option value="">All Locations</option>
                                    @foreach($hotels->pluck('address')->unique()->filter() as $address)
                                        <option value="{{ $address }}">{{ $address }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="checkin" class="form-label">
                                    <i class="fas fa-calendar me-1"></i>Check-in
                                </label>
                                <input type="date" class="form-control" id="checkin" name="checkin" 
                                       value="{{ date('Y-m-d') }}" min="{{ date('Y-m-d') }}">
                            </div>
                            <div class="col-md-2">
                                <label for="checkout" class="form-label">
                                    <i class="fas fa-calendar me-1"></i>Check-out
                                </label>
                                <input type="date" class="form-control" id="checkout" name="checkout" 
                                       value="{{ date('Y-m-d', strtotime('+1 day')) }}" min="{{ date('Y-m-d', strtotime('+1 day')) }}">
                            </div>
                            <div class="col-md-2">
                                <label for="guests" class="form-label">
                                    <i class="fas fa-users me-1"></i>Guests
                                </label>
                                <input type="text" class="form-control" id="guests" name="guests" value="1">
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-2"></i>Search
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Hotels Grid -->
    <section class="py-5">
        <div class="container">
            <div class="row mb-4">
                <div class="col-12">
                    <h2 class="text-center mb-4">Available Hotels</h2>
                    <p class="text-center text-muted">
                        <span id="hotelCount">{{ $hotels->count() }} hotel{{ $hotels->count() !== 1 ? 's' : '' }} found</span>
                        <span id="searchingIndicator" class="d-none">
                            <i class="fas fa-spinner fa-spin me-1"></i>Filtering...
                        </span>
                    </p>
                </div>
            </div>
            
            <div class="row" id="hotelsGrid">
                @foreach($hotels as $hotel)
                <div class="col-lg-4 col-md-6 mb-4 hotel-item" data-location="{{ $hotel->address }}">
                    <div class="card hotel-card h-100">
                        <div class="position-relative">
                            @if($hotel->brand_logo ?? $hotel->logo)
                                <img src="{{ asset('storage/' . ($hotel->brand_logo ?? $hotel->logo)) }}" 
                                     class="card-img-top" alt="{{ $hotel->name }}" style="height: 200px; object-fit: cover;">
                            @else
                                <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                    <i class="fas fa-hotel fa-3x text-muted"></i>
                                </div>
                            @endif
                            
                            @if($hotel->rooms->count() > 0)
                                <div class="price-badge">
                                    From ₱{{ number_format($hotel->rooms->min('price'), 0) }}
                                </div>
                            @endif
                        </div>
                        
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title">{{ $hotel->name }}</h5>
                            <p class="card-text text-muted flex-grow-1">
                                {{ Str::limit($hotel->description ?: 'Experience comfort and luxury at this beautiful hotel.', 100) }}
                            </p>
                            
                            <div class="mb-3">
                                @if($hotel->address)
                                    <small class="text-muted">
                                        <i class="fas fa-map-marker-alt me-1"></i>{{ $hotel->address }}
                                    </small>
                                @endif
                            </div>
                            
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <small class="text-muted">
                                        <i class="fas fa-bed me-1"></i>{{ $hotel->rooms->count() }} room{{ $hotel->rooms->count() !== 1 ? 's' : '' }}
                                    </small>
                                </div>
                                <div class="btn-group">
                                    <a href="{{ route('guest-portal.hotel', $hotel->slug) }}" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-info me-1"></i>Details
                                    </a>
                                    <a href="{{ route('guest-portal.book', $hotel->slug) }}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-calendar-check me-1"></i>Book
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
            
            @if($hotels->count() === 0)
            <div class="row">
                <div class="col-12 text-center py-5">
                    <i class="fas fa-hotel fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">No hotels available</h4>
                    <p class="text-muted">Please check back later for new listings.</p>
                </div>
            </div>
            @endif
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>Hotel Booking Platform</h5>
                    <p class="mb-0">Find and book the perfect hotel for your stay.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">&copy; {{ date('Y') }} Hotel Management System. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let filterTimeout = null;

        // Debounce function for filtering
        function debounceFilter() {
            if (filterTimeout) {
                clearTimeout(filterTimeout);
            }

            // Show searching indicator
            document.getElementById('hotelCount').classList.add('d-none');
            document.getElementById('searchingIndicator').classList.remove('d-none');

            filterTimeout = setTimeout(filterHotels, 300);
        }

        // Auto-update checkout date when checkin changes and trigger filter
        document.getElementById('checkin').addEventListener('change', function() {
            const checkinDate = new Date(this.value);
            const checkoutDate = new Date(checkinDate);
            checkoutDate.setDate(checkoutDate.getDate() + 1);

            const checkoutInput = document.getElementById('checkout');
            checkoutInput.min = checkoutDate.toISOString().split('T')[0];

            if (new Date(checkoutInput.value) <= checkinDate) {
                checkoutInput.value = checkoutDate.toISOString().split('T')[0];
            }

            // Auto-filter when dates change
            debounceFilter();
        });

        // Auto-filter when checkout date changes
        document.getElementById('checkout').addEventListener('change', function() {
            debounceFilter();
        });

        // Auto-filter when guest count changes
        document.getElementById('guests').addEventListener('input', function() {
            debounceFilter();
        });

        // Filter hotels by location (immediate)
        document.getElementById('location').addEventListener('change', function() {
            filterHotels();
        });

        // Main filter function
        function filterHotels() {
            const selectedLocation = document.getElementById('location').value;
            const checkin = document.getElementById('checkin').value;
            const checkout = document.getElementById('checkout').value;
            const guests = parseInt(document.getElementById('guests').value) || 1;

            const hotelItems = document.querySelectorAll('.hotel-item');
            let visibleCount = 0;

            hotelItems.forEach(item => {
                let shouldShow = true;

                // Filter by location
                if (selectedLocation && item.dataset.location !== selectedLocation) {
                    shouldShow = false;
                }

                // You can add more filters here based on dates, guests, etc.
                // For now, we'll just filter by location and show all hotels

                if (shouldShow) {
                    item.style.display = 'block';
                    visibleCount++;
                } else {
                    item.style.display = 'none';
                }
            });

            // Update count and hide loading indicator
            document.getElementById('searchingIndicator').classList.add('d-none');
            document.getElementById('hotelCount').classList.remove('d-none');
            document.getElementById('hotelCount').textContent = `${visibleCount} hotel${visibleCount !== 1 ? 's' : ''} found`;

            // Update search button URLs with current search parameters
            updateBookingLinks(checkin, checkout, guests);
        }

        // Update booking links with search parameters
        function updateBookingLinks(checkin, checkout, guests) {
            const bookButtons = document.querySelectorAll('a[href*="/book"]');
            bookButtons.forEach(button => {
                const baseUrl = button.href.split('?')[0];
                const params = new URLSearchParams();

                if (checkin) params.set('checkin', checkin);
                if (checkout) params.set('checkout', checkout);
                if (guests) params.set('guests', guests);

                button.href = baseUrl + (params.toString() ? '?' + params.toString() : '');
            });
        }

        // Search form submission (for manual search button)
        document.getElementById('searchForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const checkin = formData.get('checkin');
            const checkout = formData.get('checkout');
            const guests = formData.get('guests');
            const location = formData.get('location');

            // If a specific location is selected and we have dates, go to that hotel's booking page
            if (location && checkin && checkout) {
                const hotelCard = document.querySelector(`[data-location="${location}"]`);
                if (hotelCard) {
                    const bookButton = hotelCard.querySelector('a[href*="/book"]');
                    if (bookButton) {
                        const url = new URL(bookButton.href);
                        url.searchParams.set('checkin', checkin);
                        url.searchParams.set('checkout', checkout);
                        url.searchParams.set('guests', guests);
                        window.location.href = url.toString();
                        return;
                    }
                }
            }

            // Otherwise, just trigger the filter
            filterHotels();
        });

        // Initialize filters on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Get URL parameters if any
            const urlParams = new URLSearchParams(window.location.search);

            if (urlParams.get('checkin')) {
                document.getElementById('checkin').value = urlParams.get('checkin');
            }
            if (urlParams.get('checkout')) {
                document.getElementById('checkout').value = urlParams.get('checkout');
            }
            if (urlParams.get('guests')) {
                document.getElementById('guests').value = urlParams.get('guests');
            }

            // Apply initial filter
            filterHotels();
        });
    </script>
</body>
</html>
