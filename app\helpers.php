<?php

use App\Models\Setting;
use Illuminate\Support\Facades\Auth;
use App\Models\ActivityLog;

if (!function_exists('get_setting')) {
    function get_setting($key, $default = null)
    {
        $hotelId = session('current_hotel_id');
        if ($hotelId) {
            return Setting::where('hotel_id', $hotelId)->where('key', $key)->value('value') ?? $default;
        }
        return $default;
    }
}

if (!function_exists('get_current_hotel')) {
    function get_current_hotel()
    {
        $hotelId = session('current_hotel_id');
        if ($hotelId) {
            return \App\Models\Hotel::find($hotelId);
        }
        return null;
    }
}

if (!function_exists('get_user_email')) {
    function get_user_email()
    {
        return Auth::check() ? Auth::user()->email : 'no user';
    }
}

if (!function_exists('log_activity')) {
    function log_activity($description, $module = null)
    {
        $user_email = get_user_email();
        $hotelId = session('current_hotel_id');

        // Save the activity log to the database
        ActivityLog::create([
            'description' => $description,
            'user_email'  => $user_email,
            'module'      => $module,
            'hotel_id'    => $hotelId,
        ]);
    }
}

//Get the current user's role
if (!function_exists('get_user_role')) {
    function get_user_role()
    {
        return Auth::check() ? Auth::user()->role : 'no user';
    }
}

if (!function_exists('get_paypal_config')) {
    function get_paypal_config()
    {
        return [
            'client_id' => get_setting('paypal_client_id', env('PAYPAL_CLIENT_ID')),
            'client_secret' => get_setting('paypal_client_secret', env('PAYPAL_CLIENT_SECRET')),
            'mode' => get_setting('paypal_mode', env('PAYPAL_MODE', 'sandbox')),
            'currency' => get_setting('paypal_currency', env('PAYPAL_CURRENCY', 'PHP')),
            'base_url' => get_setting('paypal_mode', env('PAYPAL_MODE', 'sandbox')) === 'live'
                ? 'https://api-m.paypal.com'
                : 'https://api-m.sandbox.paypal.com',
        ];
    }
}

if (!function_exists('get_paymongo_config')) {
    function get_paymongo_config()
    {
        return [
            'public_key' => get_setting('paymongo_public_key', env('PAYMONGO_PUBLIC_KEY')),
            'secret_key' => get_setting('paymongo_secret_key', env('PAYMONGO_SECRET_KEY')),
        ];
    }
}

if (!function_exists('get_paypal_access_token')) {
    function get_paypal_access_token()
    {
        $config = get_paypal_config();

        if (empty($config['client_id']) || empty($config['client_secret'])) {
            throw new \Exception('PayPal credentials not configured for this hotel');
        }

        $headers = [
            'Accept' => 'application/json',
            'Accept-Language' => 'en_US',
        ];

        $response = \Illuminate\Support\Facades\Http::withHeaders($headers)
            ->withBasicAuth($config['client_id'], $config['client_secret'])
            ->asForm()
            ->post($config['base_url'] . '/v1/oauth2/token', [
                'grant_type' => 'client_credentials'
            ]);

        if ($response->successful()) {
            return $response->json()['access_token'];
        }

        throw new \Exception('Failed to get PayPal access token: ' . $response->body());
    }
}
