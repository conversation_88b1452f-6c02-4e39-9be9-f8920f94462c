<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\BelongsToHotel;

class Remittance_Payment extends Model
{
    use HasFactory, BelongsToHotel;

    protected $table = 'remmittances_payment';
    protected $fillable = [
        'hotel_id',
        'remittance_id',
        'payment_id',
        'from_user_email',
        'to_user_email',
        'amount',
        'location',
        'remittance_date',
        'confirmed_by'
    ];

    public function remittance()
    {
        return $this->belongsTo(Remittance::class, 'remittance_id');
    }

    public function payment()
    {
        return $this->belongsTo(Payment::class, 'payment_id');
    }

    
}
