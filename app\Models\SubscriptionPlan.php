<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SubscriptionPlan extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'price',
        'billing_cycle',
        'features',
        'max_rooms',
        'max_users',
        'is_active',
        'is_popular',
        'sort_order'
    ];

    protected $casts = [
        'features' => 'array',
        'price' => 'decimal:2',
        'is_active' => 'boolean',
        'is_popular' => 'boolean',
    ];

    // Relationships
    public function subscriptions()
    {
        return $this->hasMany(Subscription::class);
    }

    public function hotels()
    {
        return $this->hasManyThrough(Hotel::class, Subscription::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopePopular($query)
    {
        return $query->where('is_popular', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    // Helper methods
    public function getFormattedPriceAttribute()
    {
        return '$' . number_format($this->price, 2);
    }

    public function getYearlyPriceAttribute()
    {
        return $this->billing_cycle === 'monthly' ? $this->price * 12 : $this->price;
    }

    public function getFormattedYearlyPriceAttribute()
    {
        return '$' . number_format($this->yearly_price, 2);
    }

    public function getMonthlySavingsAttribute()
    {
        if ($this->billing_cycle === 'yearly') {
            $monthlyEquivalent = $this->price / 12;
            $regularMonthlyPrice = SubscriptionPlan::where('name', $this->name)
                ->where('billing_cycle', 'monthly')
                ->first();
            
            if ($regularMonthlyPrice) {
                return $regularMonthlyPrice->price - $monthlyEquivalent;
            }
        }
        return 0;
    }

    public function hasFeature($feature)
    {
        return in_array($feature, $this->features ?? []);
    }

    public function isUnlimitedRooms()
    {
        return $this->max_rooms === 0;
    }

    public function isUnlimitedUsers()
    {
        return $this->max_users === 0;
    }

    public function canAccommodateHotel(Hotel $hotel)
    {
        $roomCount = $hotel->rooms()->count();
        $userCount = $hotel->users()->count();

        $roomsOk = $this->isUnlimitedRooms() || $roomCount <= $this->max_rooms;
        $usersOk = $this->isUnlimitedUsers() || $userCount <= $this->max_users;

        return $roomsOk && $usersOk;
    }
}
