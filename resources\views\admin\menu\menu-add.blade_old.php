@include('layout.admin-header')
{{-- @include('layout.scripts') --}}

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        @include('layout.admin-sidebar')
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                @include('layout.admin-topbar')
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    @include('shared.success-message')
                    @include('shared.error-message')
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Menu - Add Menu Item</h6>
                            <a href="{{ route('admin.menus') }}" class="btn btn-danger float-end">BACK</a>
                        </div>
                        <div class="card-body">


                            <!-- Include Select2 CSS -->
                            <link href="{{ asset('assets/css/select2.min.css') }}" rel="stylesheet" />
                            <!-- Include Select2 JS -->
                            <script src="{{ asset('assets/js/select2.min.js') }}" defer></script>
                            {{-- Form to Create Booking --}}
                            <form action="{{ route('admin.booking.store') }}" method="POST">
                                @csrf
                                <div class="row">
                                    <div class="form-group col-md-3 mb-3">
                                        <label for="">Menu Item Name</label>
                                        <input type="text" name="name" required class="form-control">
                                    </div>
                                    <div class="form-group col-md-12 mb-3">
                                        <label for="">Description</label>
                                        <textarea name="description" required class="form-control"></textarea>
                                    </div>
                                    <div class="form-group col-md-12 mb-3">
                                        <label for="">How to Cook?</label>
                                        <textarea name="how_to_cook" required class="form-control"></textarea>
                                    </div>
                                    <div class="form-group col-md-6 mb-3">
                                        <label for="">Price</label>
                                        <input type="number" name="price" id="subtotal" required
                                            class="form-control">
                                    </div>
                                    <div class="form-group col-md-6 mb-3">
                                        <label for="">Cook Time</label>
                                        <input type="text" name="cook_time" required class="form-control">
                                    </div>


                                </div>

                                <div class="form-group col-md-12 mb-3">
                                    <label for="">Menu Item Ingredients</label>
                                    <div id="menu_items_container">
                                        <!-- menu items will be appended here -->
                                    </div>
                                    <button type="button" class="btn btn-success" onclick="addMenuItem()">Add Menu
                                        Ingredient</button>
                                </div>



                                <div class="form-group col-md-12 mb-3">
                                    <button type="submit" name="menuAdd" class="btn btn-primary">Create Menu
                                        Item</button>
                                </div>
                            </form>




                        </div>
                    </div>
                </div>
            </div>


            <!-- End of Main Content -->
            <!-- Footer -->
            @include('layout.admin-footer')
            <!-- End of Footer -->


        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary" href="login.html">Logout</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap core JavaScript-->
    <script src="{{ asset('assets/js/jquery.min.js') }}"></script>
    <script src="{{ asset('assets/js/bootstrap.bundle.min.js') }}"></script>

    <!-- Core plugin JavaScript-->
    <script src="{{ asset('assets/js/jquery.easing.min.js') }}"></script>

    <!-- Custom scripts for all pages-->
    <script src="{{ asset('assets/js/sb-admin-2.min.js') }}"></script>

    <!-- Datetime Picker JS and CSS-->
    <script src="{{ asset('assets/js/datetimepicker.js') }}"></script>

    <!-- JavaScript Initialize for Date Picker -->
    <script>
        $(document).ready(function() {
            $('#checkin_date').datetimepicker({
                format: 'Y-m-d h:i A', // Full date and time format
                step: 30 // 30 minutes
            });

            $('#checkout_date').datetimepicker({
                format: 'Y-m-d h:i A', // Full date and time format
                step: 30 // 30 minutes
            });
        });
    </script>

@php
        // Initialize an empty array to store menu items
        $ingredientOptions = [];
        // Populate the array with menu items
        foreach ($ingredient_list as $ingredient) {
            $ingredientOptions[] = ['id' => $ingredient->id, 'name' => $ingredient->ingredient_name, 'price' => $ingredient->price];
        }
    @endphp

    <script>
        let menuItemCount = 0;
        let ingredientOptions = @json($ingredientOptions);

        // Function to add order item dynamically
        function addMenuItem() {
            menuItemCount++;
            const container = document.getElementById('menu_items_container');
            const menuItemDiv = document.createElement('div');
            menuItemDiv.className = 'order-item row mb-3';
            menuItemDiv.id = `order_item_${menuItemCount}`;

            // Create a dropdown menu for ingredients name
            const menuSelect = document.createElement('select');
            menuSelect.name = `order_items[${menuItemCount}][menu_id]`;
            menuSelect.className = 'form-control';
            menuSelect.required = true;

            // Populate the dropdown with menu items
            ingredientOptions.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option.id;
                optionElement.textContent = option.name;
                menuSelect.appendChild(optionElement);
            });

            menuItemDiv.innerHTML = `
            <div class="col-md-3">
                <label>Item Name</label>
                ${menuSelect.outerHTML}
            </div>
            <div class="col-md-3">
                <label>Quantity</label>
                <input type="number" name="order_items[${menuItemCount}][quantity]" class="form-control" required>
            </div>
            <div class="col-md-3">
                <label>Unit (Kg,Mg,g, etc.)</label>
                <input type="text" name="order_items[${menuItemCount}][unit]" class="form-control" required>
            </div>
            
            <div class="col-md-3 py-3 text-right">
                <button type="button" class="btn btn-danger" onclick="removeOrderItem(${menuItemCount})">Remove</button>
            </div>
        `;
            container.appendChild(menuItemDiv);
        }

        // Function to remove order item
        function removeOrderItem(itemId) {
            const item = document.getElementById(`order_item_${itemId}`);
            item.remove();
        }

        // Function to calculate subtotal dynamically
        function calculateSubtotal() {
            let subtotal = 0;
            console.log('Calculating subtotal...');
            console.log(ingredientOptions);

            // Loop through each order item
            const orderItems = document.querySelectorAll('.order-item');
            orderItems.forEach(item => {
                const menuId = item.querySelector('select').value;
                const quantity = parseInt(item.querySelector('input').value);
                const menuItem = ingredientOptions.find(option => option.id == menuId);
                if (menuItem && !isNaN(quantity) && quantity > 0) {
                    subtotal += menuItem.price * quantity;
                }
            });

            return subtotal.toFixed(2); // Return subtotal with 2 decimal places
        }



        // Function to update subtotal display
        function updateSubtotalDisplay() {
            const subtotalDisplay = document.getElementById('subtotal_display');
            const subtotalInput = document.getElementById('subtotal');
            const subtotal = calculateSubtotal();

            if (subtotalDisplay) {
                subtotalDisplay.textContent = `$${subtotal}`;
            }

            if (subtotalInput) {
                subtotalInput.placeholder = 'Minimum Recommended Price: '+subtotal;
            }
        }

        // Listen for input change events to update subtotal
        document.addEventListener('input', updateSubtotalDisplay);

        // Initially update subtotal display
        updateSubtotalDisplay();
    </script>


</body>

</html>
