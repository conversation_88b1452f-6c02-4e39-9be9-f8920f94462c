<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\BelongsToHotel;

class Order extends Model
{
    use HasFactory, BelongsToHotel;

    protected $fillable = [
        'hotel_id',
        'customer_name',  // Optional, if you want to track who ordered
        'table_name',   // vince orig order number
        'order_status',   // Pending, Completed, etc.
        'total_price',
		'room_charge', //Room Charge Boolean
        'created_by',
        'updated_by',
        'invoice_id' //vince added
    ];

    public function orderItems()
    {
        return $this->hasMany(OrderItem::class);
    }

    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    public function invoice()//vince added function
    {
        return $this->hasOne(Invoice::class, 'id', 'invoice_id');
    }
}
