@extends('guest-portal.layout')

@section('title', 'Welcome')

@section('header')
    <div class="row align-items-center">
        <div class="col-lg-8">
            <h1 class="display-4 fw-bold mb-3">Welcome to {{ $hotel->name }}</h1>
            @if($hotel->description)
                <p class="lead mb-4">{{ $hotel->description }}</p>
            @endif
            <div class="d-flex flex-wrap gap-3">
                <a href="{{ route('guest-portal.access', $hotel->slug) }}" class="btn btn-light btn-lg">
                    <i class="fas fa-sign-in-alt me-2"></i>Access Your Bookings
                </a>
                <a href="{{ route('guest-portal.book', $hotel->slug) }}" class="btn btn-outline-light btn-lg">
                    <i class="fas fa-bed me-2"></i>Book a Room
                </a>
            </div>
        </div>
        <div class="col-lg-4 text-center">
            @if($hotel->brand_logo ?? $hotel->logo)
                <img src="{{ asset('storage/' . ($hotel->brand_logo ?? $hotel->logo)) }}" 
                     alt="{{ $hotel->name }}" 
                     class="img-fluid rounded shadow"
                     style="max-height: 200px;">
            @endif
        </div>
    </div>
@endsection

@section('content')
<div class="row">
    <!-- Features Section -->
    <div class="col-12 mb-5">
        <h2 class="text-center mb-4 text-hotel-primary">Guest Portal Features</h2>
        <div class="row g-4">
            <div class="col-md-4">
                <div class="card h-100 text-center">
                    <div class="card-body">
                        <div class="text-hotel-primary mb-3">
                            <i class="fas fa-mobile-alt fa-3x"></i>
                        </div>
                        <h5 class="card-title">Mobile Friendly</h5>
                        <p class="card-text">Access your bookings and make payments from any device, anywhere.</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 text-center">
                    <div class="card-body">
                        <div class="text-hotel-primary mb-3">
                            <i class="fas fa-bed fa-3x"></i>
                        </div>
                        <h5 class="card-title">Easy Booking</h5>
                        <p class="card-text">Book rooms online with real-time availability and instant confirmation.</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 text-center">
                    <div class="card-body">
                        <div class="text-hotel-primary mb-3">
                            <i class="fas fa-credit-card fa-3x"></i>
                        </div>
                        <h5 class="card-title">Secure Payments</h5>
                        <p class="card-text">Pay your invoices securely using PayPal, GCash, PayMaya, and more.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Access Section -->
    <div class="col-12">
        <div class="row g-4">
            <div class="col-md-6">
                <div class="card border-hotel-primary">
                    <div class="card-header bg-hotel-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-user-check me-2"></i>Existing Guests
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">Already have a booking or invoice? Access your information using your email and booking/invoice reference.</p>
                        <a href="{{ route('guest-portal.access', $hotel->slug) }}" class="btn btn-hotel-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>Access Portal
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card border-hotel-primary">
                    <div class="card-header bg-hotel-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-plus-circle me-2"></i>New Guests
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">Looking to make a new reservation? Browse our available rooms and book instantly.</p>
                        <a href="{{ route('guest-portal.book', $hotel->slug) }}" class="btn btn-hotel-primary">
                            <i class="fas fa-bed me-2"></i>Book Now
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hotel Information Section -->
@if($hotel->address || $hotel->phone || $hotel->email || $hotel->website)
<div class="row mt-5">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>Hotel Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    @if($hotel->address)
                    <div class="col-md-6 mb-3">
                        <h6><i class="fas fa-map-marker-alt text-hotel-primary me-2"></i>Address</h6>
                        <p class="text-muted">{{ $hotel->address }}</p>
                    </div>
                    @endif
                    
                    @if($hotel->phone)
                    <div class="col-md-6 mb-3">
                        <h6><i class="fas fa-phone text-hotel-primary me-2"></i>Phone</h6>
                        <p class="text-muted">
                            <a href="tel:{{ $hotel->phone }}" class="text-decoration-none">{{ $hotel->phone }}</a>
                        </p>
                    </div>
                    @endif
                    
                    @if($hotel->email)
                    <div class="col-md-6 mb-3">
                        <h6><i class="fas fa-envelope text-hotel-primary me-2"></i>Email</h6>
                        <p class="text-muted">
                            <a href="mailto:{{ $hotel->email }}" class="text-decoration-none">{{ $hotel->email }}</a>
                        </p>
                    </div>
                    @endif
                    
                    @if($hotel->website)
                    <div class="col-md-6 mb-3">
                        <h6><i class="fas fa-globe text-hotel-primary me-2"></i>Website</h6>
                        <p class="text-muted">
                            <a href="{{ $hotel->website }}" target="_blank" class="text-decoration-none">{{ $hotel->website }}</a>
                        </p>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endif
@endsection
