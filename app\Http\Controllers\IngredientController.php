<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Ingredient;
use App\Models\Menu;//vince added
use App\Models\Menu_Ingredient;//vince added

class IngredientController extends Controller
{
    public function index()
    {
        $ingredients = Ingredient::all();
        $title = 'Stock Inventory';//vince added
        return view('admin.ingredients.ingredient-view', ['ingredients_list' => $ingredients, 'title' => $title]);
    }

    public function add()
    {
        return view('admin.ingredients.ingredient-add');
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'ingredient_name' => 'required | string',
            'supplier' => 'string|nullable',
            'minqty' => 'numeric',
            'unit' => 'required | string',
            'cost' => 'numeric',
            'stock' => 'numeric',
            'for_pos' => 'numeric', //vince added
            'price' => 'numeric' //vince added
        ]);

        // Create a new ingredient model instance and fill it with the validated data
        $ingredient = new Ingredient();
        $validatedData['cost'] = number_format($validatedData['cost'],2);//vince added
        $ingredient->fill($validatedData);

        // Save the new ingredient to the database
        $ingredient->save();

        //Log activity
        log_activity("Ingredient added: " . $ingredient->ingredient_name, 'Ingredient controller');

        //vince create a menu item for POS if needed
        if (isset($validatedData['for_pos']) && $validatedData['for_pos'] == 1) {
            $this->createMenuForPOS($ingredient->id, $validatedData['price'] );
        }
        //vince end
        return redirect()->route('admin.ingredients');
    }

    //vince added function
    private function createMenuForPOS($ingredientID, $price) {
        $ingredient = Ingredient::findOrFail($ingredientID);
        $menuData = [
            'name' => $ingredient->ingredient_name,
            'description' => $ingredient->ingredient_name,
            'how_to_cook' => '',
            'price' => $price,
            'cook_time' => '',
            'menu_ingredients' => [$ingredient],
            //'menu_ingredients.*.menu_id' => 'required|exists:ingredients,id',
            //'menu_ingredients.*.quantity' => 'required|numeric',
            //'menu_ingredients.*.unit' => 'required|string|max:50',
        ];

        $menu = new Menu();
        $menu->fill($menuData);
        $menu->save();

        $menuIngredient = new Menu_Ingredient();
        $menuIngredient->menu_id = $menu->id;
        $menuIngredient->ingredient_id = $ingredientID;
        $menuIngredient->quantity = 1;
        $menuIngredient->unit = $ingredient['unit'];
        $menuIngredient->save();

        //Log activity
        log_activity("Menu added: " . $menu->name, 'Ingredient controller');
    }

    public function edit($id)
    {
        $ingredient = Ingredient::find($id);
        if ($ingredient) {
            return view('admin.ingredients.ingredient-edit', ['ingredient' => $ingredient]);
        } else {
            return redirect()->route('admin.ingredients')->with('error', 'Item not found.');;
        }
    }

    public function update(Request $request, $id)
    {
        $validatedData = $request->validate([
            'ingredient_name' => 'required | string',
            'supplier' => 'string|nullable',
            'minqty' => 'numeric',
            'unit' => 'required | string',
            'cost' => 'numeric',
            'stock' => 'numeric',
            'for_pos' => 'numeric', //vince added
            'price' => 'numeric' //vince added
        ]);

        $ingredient = Ingredient::findOrFail($id);
        $validatedData['cost'] = number_format($validatedData['cost'],2);//vince added 
        $ingredient->fill($validatedData);
        $ingredient->save();

        //Log activity
        log_activity("Ingredient updated: " . $ingredient->ingredient_name, 'Ingredient Controlller');

        //vince create a menu item for POS if needed
        if (isset($validatedData['for_pos']) && $validatedData['for_pos'] == 1) {
            $this->createMenuForPOS($ingredient->id, $validatedData['price'] );
        }
        //vince end

        return redirect()->route('admin.ingredients');
    }

    public function destroy($id)
    {
        $ingredient = Ingredient::findOrFail($id);
        $ingredient->delete();

        return redirect()->route('admin.ingredients');
    }
}
