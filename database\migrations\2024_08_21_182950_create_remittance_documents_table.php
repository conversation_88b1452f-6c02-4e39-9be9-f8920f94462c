<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('remittance_documents', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('remittance_id');
            $table->string('file_name');
            $table->string('file_type');
            $table->string('file_size');
            $table->string('file_path');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('remittance_documents');
    }
};
