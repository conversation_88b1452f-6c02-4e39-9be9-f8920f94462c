@include('layout.admin-header')
@php
    $currencySymbol = config('currency.symbol');
@endphp

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        @include('layout.admin-sidebar')
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                @include('layout.admin-topbar')
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid px-4">
                    
                    @include ('shared.error-message')
                    @include ('shared.success-message')
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h4 class="mt-4">Restaurant Orders</h4>
                            <a href="{{route('admin.order.add')}}" class="btn btn-primary float-right"><i class="fa fa-plus"></i> Add Order</a>
                        </div>
            
                    {{-- Display Orders with Menu Items --}}
                    <div class="card-body">
                        <div class="table-responsive">
                                
                                <table id="myRequests" class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>Order ID</th>
                                            <th>Table</th>
                                            <th>Customer Name</th>
                                            <th>Order Status</th>
                                            <th>Total Price</th>
                                            <th>Created</th>
                                            <th>Charged To Room?</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($orders as $order)
                                            <tr>
                                                <td>{{ $order->id }}</td>
                                                <td>{{ $order->table_name }}</td>
                                                <td>{{ $order->customer_name }}</td>
                                                <td>{{ $order->order_status }}</td>
                                                <td style="text-align:right;">{{ $currencySymbol . number_format($order->total_price, 2) }}</td>
                                                <td>{{ date('M j, Y g:i A', strtotime($order->created_at)) }}</td>
                                                <td style="text-align:center;">{{ $order->room_charge ? 'Yes' : 'No' }}</td>
                                                <td>
                                                    <a href="{{ route('admin.order.edit', $order->id) }}"
                                                        class="btn btn-primary btn-circle btn-sm" title="Edit Order">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form action="{{ route('admin.order.destroy', $order->id) }}"
                                                        method="POST" style="display: inline;">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-danger btn-circle btn-sm" title="Delete Order">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>

                                                    <!-- Edit Invoice Button -->
                                                    @if (!is_null($order->invoice_id))
                                                        <a href="{{ route('admin.posinvoice.view',  $order->invoice_id) }}" 
                                                            class="btn btn-info btn-circle btn-sm" title="View Invoice">
                                                            <i class="fas fa-file-invoice"></i>
                                                        </a>        
                                                    @endif
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            @include('layout.admin-footer')
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary" href="login.html">Logout</a>
                </div>
            </div>
        </div>
    </div>

    @include('layout.scripts')

</body>



</html>
