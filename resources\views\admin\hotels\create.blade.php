@include('layout.admin-header')

<body id="page-top">
    <!-- Page Wrapper -->
    <div id="wrapper" data-bs-theme="light">
        <!-- Sidebar -->
        @include('layout.admin-sidebar')
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">
            <!-- Main Content -->
            <div id="content">
                <!-- Topbar -->
                @include('layout.admin-topbar')
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    @include('shared.success-message')
                    @include('shared.error-message')

                    <!-- Page Heading -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">Add New Hotel</h1>
                        <a href="{{ route('admin.hotels.index') }}" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
                            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Hotels
                        </a>
                    </div>

                    <!-- Hotel Form -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Hotel Information</h6>
                        </div>
                        <div class="card-body">
                            <form action="{{ route('admin.hotels.store') }}" method="POST">
                                @csrf
                                
                                <div class="row">
                                    <!-- Hotel Details -->
                                    <div class="col-md-6">
                                        <h5 class="text-primary mb-3">Hotel Details</h5>
                                        
                                        <div class="form-group">
                                            <label for="name">Hotel Name *</label>
                                            <input type="text" name="name" id="name" class="form-control @error('name') is-invalid @enderror" 
                                                   value="{{ old('name') }}" required>
                                            @error('name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="form-group">
                                            <label for="email">Hotel Email *</label>
                                            <input type="email" name="email" id="email" class="form-control @error('email') is-invalid @enderror" 
                                                   value="{{ old('email') }}" required>
                                            @error('email')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="form-group">
                                            <label for="phone">Phone</label>
                                            <input type="text" name="phone" id="phone" class="form-control @error('phone') is-invalid @enderror" 
                                                   value="{{ old('phone') }}">
                                            @error('phone')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="form-group">
                                            <label for="address">Address</label>
                                            <textarea name="address" id="address" class="form-control @error('address') is-invalid @enderror" 
                                                      rows="3">{{ old('address') }}</textarea>
                                            @error('address')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="form-group">
                                            <label for="tin">TIN</label>
                                            <input type="text" name="tin" id="tin" class="form-control @error('tin') is-invalid @enderror" 
                                                   value="{{ old('tin') }}">
                                            @error('tin')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="form-group">
                                            <label for="website">Website</label>
                                            <input type="url" name="website" id="website" class="form-control @error('website') is-invalid @enderror" 
                                                   value="{{ old('website') }}">
                                            @error('website')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="form-group">
                                            <label for="description">Description</label>
                                            <textarea name="description" id="description" class="form-control @error('description') is-invalid @enderror" 
                                                      rows="3">{{ old('description') }}</textarea>
                                            @error('description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <!-- Admin User Details -->
                                    <div class="col-md-6">
                                        <h5 class="text-primary mb-3">Admin User Details</h5>
                                        
                                        <div class="form-group">
                                            <label for="admin_name">Admin Name *</label>
                                            <input type="text" name="admin_name" id="admin_name" class="form-control @error('admin_name') is-invalid @enderror" 
                                                   value="{{ old('admin_name') }}" required>
                                            @error('admin_name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="form-group">
                                            <label for="admin_email">Admin Email *</label>
                                            <input type="email" name="admin_email" id="admin_email" class="form-control @error('admin_email') is-invalid @enderror" 
                                                   value="{{ old('admin_email') }}" required>
                                            @error('admin_email')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="form-group">
                                            <label for="admin_password">Admin Password *</label>
                                            <input type="password" name="admin_password" id="admin_password" class="form-control @error('admin_password') is-invalid @enderror" 
                                                   required>
                                            @error('admin_password')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">Minimum 8 characters</small>
                                        </div>

                                        <div class="alert alert-info">
                                            <h6><i class="fas fa-info-circle"></i> What happens when you create a hotel:</h6>
                                            <ul class="mb-0">
                                                <li>A new hotel account will be created</li>
                                                <li>An admin user will be created for the hotel</li>
                                                <li>Default room types will be set up</li>
                                                <li>Default settings will be configured</li>
                                                <li>The hotel will get a 30-day trial period</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                <hr>
                                
                                <div class="form-group">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> Create Hotel
                                    </button>
                                    <a href="{{ route('admin.hotels.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> Cancel
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <!-- /.container-fluid -->
            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            @include('layout.admin-footer')
            <!-- End of Footer -->
        </div>
        <!-- End of Content Wrapper -->
    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>
</body>
