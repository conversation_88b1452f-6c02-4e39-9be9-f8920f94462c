<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;

class UserController extends Controller
{
    public function index()
    {
        $users = User::all();
        return view('admin.user.view-user', compact('users'));
    }

    public function staff()
    {
        $staffs = User::where('role', 'staff')->get();
        return view('admin.user.view-staff', compact('staffs'));
    }

    public function edit($id)
    {
        $user = User::find($id);
        $roles = User::select('role')->distinct()->get();
        return view('admin.user.edit-user', compact('user', 'roles'));
    }

    public function add()
    {
        return view('admin.user.add-user');
    }

    public function store()
    {
        $user = new User();
        $user->name = request('name');
        $user->email = request('email');
        $user->role = request('role');
        $user->password = bcrypt(request('password'));
        $user->save();

        //Log activity
        log_activity("User added: " . $user->name, 'User Module');

        return redirect()->route('admin.users');
    }

    public function update()
    {
        $user = User::find(request('id'));
        $user->name = request('name');
        $user->email = request('email');
        $user->role = request('role');
        $user->save();

        //Log activity
        log_activity("User updated: " . $user->name, 'User Module');

        return redirect()->route('admin.users');
    }

    public function create()
    {
        return view('admin.user.create');
    }


}
