<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\BelongsToHotel;

class Event_Item extends Model
{
    use HasFactory, BelongsToHotel;

    protected $table = 'event_items'; // Specify the custom table name
    protected $fillable = [
        'id',
        'hotel_id',
        'event_id',
        'menu_id',
        'quantity',
        'price',
        'bought_grocery',
        'kitchen_confirmed',
        'created_at',
        'updated_at',
    ];

    public function event()
    {
        return $this->belongsTo(Event::class);
    }

    public function menu()
    {
        return $this->belongsTo(Menu::class);
    }

    public function groceries()
    {
        return $this->hasMany(Grocery_Item::class);
    }
}
