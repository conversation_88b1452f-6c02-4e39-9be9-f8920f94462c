<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('rooms', function (Blueprint $table) {
            $table->id();
            $table->string('room_number')->unique();
            $table->foreignId('room_type')->nullable()->references('id')->on('room_types');//vince added nullable
            $table->decimal('individual_room_rate', 10, 2);
            $table->string('floor')->nullable();//vince added nullable
            $table->boolean('is_booked')->default(false);
            $table->dateTime('booked_at')->nullable();
            $table->dateTime('booked_until')->nullable();
            $table->foreignId('guest_id')->nullable()->references('id')->on('guests');
            $table->timestamps();
        });

        //insert rooms
        DB::table('rooms')->insert([
            ['room_number' => '101', 'room_type' => 1, 'individual_room_rate' => 1000.00, 'floor' => '1st'],
            ['room_number' => '102', 'room_type' => 1, 'individual_room_rate' => 1000.00, 'floor' => '1st'],
            ['room_number' => '103', 'room_type' => 2, 'individual_room_rate' => 1500.00, 'floor' => '1st'],
            ['room_number' => '104', 'room_type' => 2, 'individual_room_rate' => 1500.00, 'floor' => '1st'],
            ['room_number' => '105', 'room_type' => 3, 'individual_room_rate' => 2000.00, 'floor' => '1st'],
            ['room_number' => '106', 'room_type' => 3, 'individual_room_rate' => 2000.00, 'floor' => '1st'],
            ['room_number' => '107', 'room_type' => 4, 'individual_room_rate' => 2500.00, 'floor' => '1st'],
            ['room_number' => '108', 'room_type' => 4, 'individual_room_rate' => 2500.00, 'floor' => '1st'],
            ['room_number' => '201', 'room_type' => 1, 'individual_room_rate' => 1000.00, 'floor' => '2nd'],
            ['room_number' => '202', 'room_type' => 1, 'individual_room_rate' => 1000.00, 'floor' => '2nd'],
            ['room_number' => '203', 'room_type' => 2, 'individual_room_rate' => 1500.00, 'floor' => '2nd'],
            ['room_number' => '204', 'room_type' => 2, 'individual_room_rate' => 1500.00, 'floor' => '2nd'],
            ['room_number' => '205', 'room_type' => 3, 'individual_room_rate' => 2000.00, 'floor' => '2nd'],
            ['room_number' => '206', 'room_type' => 3, 'individual_room_rate' => 2000.00, 'floor' => '2nd'],
            ['room_number' => '207', 'room_type' => 4, 'individual_room_rate' => 2500.00, 'floor' => '2nd'],
            ['room_number' => '208', 'room_type' => 4, 'individual_room_rate' => 2500.00, 'floor' => '2nd'],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('rooms');
    }
};
