<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Hotel;
use App\Models\Setting;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

class PaymentGatewayConfigTest extends TestCase
{
    use RefreshDatabase;

    protected $hotel;
    protected $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a test hotel
        $this->hotel = Hotel::create([
            'name' => 'Test Hotel',
            'slug' => 'test-hotel',
            'email' => '<EMAIL>',
            'status' => 'active'
        ]);

        // Create a test user
        $this->user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'admin'
        ]);

        // Associate user with hotel
        $this->hotel->users()->attach($this->user->id, [
            'role' => 'admin',
            'is_active' => true
        ]);

        // Set current hotel in session
        session(['current_hotel_id' => $this->hotel->id]);
    }

    public function test_paypal_config_uses_hotel_settings()
    {
        // Set hotel-specific PayPal settings
        Setting::create([
            'hotel_id' => $this->hotel->id,
            'key' => 'paypal_client_id',
            'value' => 'hotel_paypal_client_id'
        ]);

        Setting::create([
            'hotel_id' => $this->hotel->id,
            'key' => 'paypal_client_secret',
            'value' => 'hotel_paypal_secret'
        ]);

        Setting::create([
            'hotel_id' => $this->hotel->id,
            'key' => 'paypal_mode',
            'value' => 'live'
        ]);

        Setting::create([
            'hotel_id' => $this->hotel->id,
            'key' => 'paypal_currency',
            'value' => 'USD'
        ]);

        $config = get_paypal_config();

        $this->assertEquals('hotel_paypal_client_id', $config['client_id']);
        $this->assertEquals('hotel_paypal_secret', $config['client_secret']);
        $this->assertEquals('live', $config['mode']);
        $this->assertEquals('USD', $config['currency']);
        $this->assertEquals('https://api-m.paypal.com', $config['base_url']);
    }

    public function test_paypal_config_falls_back_to_env()
    {
        $config = get_paypal_config();

        // Since no hotel settings exist, should fall back to env values (which may be null in testing)
        $this->assertEquals(env('PAYPAL_CLIENT_ID'), $config['client_id']);
        $this->assertEquals(env('PAYPAL_CLIENT_SECRET'), $config['client_secret']);
        $this->assertEquals(env('PAYPAL_MODE', 'sandbox'), $config['mode']);
        $this->assertEquals(env('PAYPAL_CURRENCY', 'PHP'), $config['currency']);

        // Base URL should be determined by mode
        $expectedBaseUrl = (env('PAYPAL_MODE', 'sandbox') === 'live')
            ? 'https://api-m.paypal.com'
            : 'https://api-m.sandbox.paypal.com';
        $this->assertEquals($expectedBaseUrl, $config['base_url']);
    }

    public function test_paymongo_config_uses_hotel_settings()
    {
        // Set hotel-specific PayMongo settings
        Setting::create([
            'hotel_id' => $this->hotel->id,
            'key' => 'paymongo_public_key',
            'value' => 'pk_test_hotel_key'
        ]);

        Setting::create([
            'hotel_id' => $this->hotel->id,
            'key' => 'paymongo_secret_key',
            'value' => 'sk_test_hotel_secret'
        ]);

        $config = get_paymongo_config();

        $this->assertEquals('pk_test_hotel_key', $config['public_key']);
        $this->assertEquals('sk_test_hotel_secret', $config['secret_key']);
    }

    public function test_paymongo_config_falls_back_to_env()
    {
        $config = get_paymongo_config();

        // Should fall back to env values since no hotel settings exist
        $this->assertEquals(env('PAYMONGO_PUBLIC_KEY'), $config['public_key']);
        $this->assertEquals(env('PAYMONGO_SECRET_KEY'), $config['secret_key']);
    }

    public function test_get_setting_function_works_with_hotel_context()
    {
        Setting::create([
            'hotel_id' => $this->hotel->id,
            'key' => 'test_setting',
            'value' => 'hotel_specific_value'
        ]);

        $value = get_setting('test_setting', 'default_value');
        $this->assertEquals('hotel_specific_value', $value);

        $nonExistentValue = get_setting('non_existent_setting', 'default_value');
        $this->assertEquals('default_value', $nonExistentValue);
    }
}
