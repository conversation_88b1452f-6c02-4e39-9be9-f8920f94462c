<div id="wrapper">
    <!-- Sidebar -->
    <ul class="navbar-nav bg-gradient-primary sidebar sidebar-dark accordion" id="accordionSidebar">

        <!-- Sidebar - Brand -->
        <a class="sidebar-brand d-flex align-items-center justify-content-center" href="<?php echo e(route('admin.index')); ?>">
            <div class="sidebar-brand-icon rotate-n-15">
                <i class="fas fa-laugh-wink text-white"></i>
            </div>
            <div class="sidebar-brand-text text-white mx-3">
                <?php echo e(get_setting('site_name', 'Hotel Management System')); ?>

            </div>
        </a>

        <!-- Divider -->
        <hr class="sidebar-divider my-0">

        <!-- Hotel Selector -->
        <?php if(auth()->user() && (auth()->user()->isSuperAdmin() || auth()->user()->hotels()->count() > 1)): ?>
        <li class="nav-item">
            <div class="nav-link">
                <div class="d-flex align-items-center">
                    <i class="fas fa-building text-white mr-2"></i>
                    <select id="hotel-selector" class="form-control form-control-sm bg-primary text-white border-0" style="font-size: 0.8rem;">
                        <?php if(auth()->user()->isSuperAdmin()): ?>
                            <?php $__currentLoopData = \App\Models\Hotel::all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $hotel): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($hotel->id); ?>" <?php echo e(session('current_hotel_id') == $hotel->id ? 'selected' : ''); ?>>
                                    <?php echo e($hotel->name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                            <?php $__currentLoopData = auth()->user()->hotels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $hotel): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($hotel->id); ?>" <?php echo e(session('current_hotel_id') == $hotel->id ? 'selected' : ''); ?>>
                                    <?php echo e($hotel->name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                    </select>
                </div>
            </div>
        </li>
        <?php endif; ?>

        <!-- Super Admin Hotel Management -->
        <?php if(auth()->user() && auth()->user()->isSuperAdmin()): ?>
        <li class="nav-item">
            <a class="nav-link" href="<?php echo e(route('admin.hotels.index')); ?>">
                <i class="fas fa-fw fa-building"></i>
                <span>Manage Hotels</span>
            </a>
        </li>
        <?php endif; ?>

        <!-- Nav Item - Dashboard -->
        <li class="nav-item active">
            <a class="nav-link" href="<?php echo e(route('admin.index')); ?>">
                <i class="fas fa-fw fa-tachometer-alt"></i>
                <span>Dashboard</span></a>
        </li>

        <!-- Nav Item - Events -->
        <li class="nav-item">
            <a class="nav-link collapsed" href="#" data-toggle="collapse" data-target="#collapseEvents"
                aria-expanded="true" aria-controls="collapseEvents">
                <i class="fas fa-glass-cheers"></i>
                <span>Events</span>
            </a>
            <div id="collapseEvents" class="collapse" aria-labelledby="headingEvents" data-parent="#accordionSidebar">
                <div class="bg-white py-2 collapse-inner rounded">
                    <h6 class="collapse-header">Events:</h6>
                    <a class="collapse-item" href="<?php echo e(route('admin.event.add')); ?>">Add Events</a>
                    <a class="collapse-item" href="<?php echo e(route('admin.events')); ?>">Events</a>
                </div>
            </div>
        </li>

        <!-- Divider -->
        <hr class="sidebar-divider">

        <!-- Heading -->
        <div class="sidebar-heading">
            Hotel Core Features
        </div>

        <!-- Nav Item - Hotel Core Features -->

        <li class="nav-item">
            <a class="nav-link collapsed" href="#" data-toggle="collapse" data-target="#collapseHotelFeatures"
                aria-expanded="true" aria-controls="collapseHotelFeatures">
                <i class="fas fa-hotel"></i>
                <span>Hotel Core Features</span>
            </a>
            <div id="collapseHotelFeatures" class="collapse" aria-labelledby="headingHotelFeatures"
                data-parent="#accordionSidebar">
                <div class="bg-white py-2 collapse-inner rounded">
                    <h6 class="collapse-header">Core Features:</h6>

                    <!-- Bookings -->
                    <a class="collapse-item" href="<?php echo e(route('admin.bookings')); ?>">
                        <i class="fas fa-fw fa-bed"></i>
                        <span>Bookings</span>
                    </a>

                    <!-- Room Management -->
                    <a class="collapse-item" href="<?php echo e(route('admin.rooms')); ?>">
                        <i class="fas fa-fw fa-bed"></i>
                        <span>Rooms</span>
                    </a>
                    <a class="collapse-item" href="<?php echo e(route('admin.roomtypes')); ?>">
                        <i class="fas fa-fw fa-bed"></i>
                        <span>Room Types</span>
                    </a>

                    <!-- Invoices -->
                    <a class="collapse-item" href="<?php echo e(route('admin.invoices')); ?>">
                        <i class="fas fa-file-invoice"></i>
                        <span>Invoices</span>
                    </a>

                    <!-- Payments -->
                    <a class="collapse-item" href="<?php echo e(route('admin.payments')); ?>">
                        <i class="fa fa-credit-card"></i>
                        <span>Payments</span>
                    </a>

                    <!-- Remittance -->
                    <a class="collapse-item" href="<?php echo e(route('admin.remittances')); ?>">
                        <i class="fas fa-money-bill"></i>
                        <span>Remittance</span>
                    </a>
                    <!-- Expenses -->
                    <a class="collapse-item" href="<?php echo e(route('admin.expenses')); ?>">
                        <i class="fas fa-money-bill-wave"></i>
                        <span>Expenses</span>
                    </a>
                </div>
            </div>
        </li>





        <!-- Divider -->
        <hr class="sidebar-divider">

        <!-- Heading -->
        <div class="sidebar-heading">
            Addons
        </div>



        <!-- Nav Item - Food Management -->
        <li class="nav-item">
            <a class="nav-link collapsed" href="#" data-toggle="collapse" data-target="#collapseFood"
                aria-expanded="true" aria-controls="collapseFood">
                <i class="fas fa-book"></i>
                <span>Inventory Management</span>
            </a>
            <div id="collapseFood" class="collapse" aria-labelledby="headingFood" data-parent="#accordionSidebar">
                <div class="bg-white py-2 collapse-inner rounded">
                    <h6 class="collapse-header">Manage Food:</h6>
                    <a class="collapse-item" href="<?php echo e(route('admin.menus')); ?>">
                        <i class="fas fa-hamburger"></i> Food Menu
                    </a>
                    <a class="collapse-item" href="<?php echo e(route('admin.kitchenView')); ?>">
                        <i class="fas fa-bread-slice"></i> Kitchen
                    </a>
                    <a class="collapse-item" href="<?php echo e(route('admin.groceryView')); ?>">
                        <i class="fas fa-shopping-basket"></i> Grocery
                    </a>
                    <a class="collapse-item" href="<?php echo e(route('admin.ingredients')); ?>">
                        <i class="fas fa-carrot"></i> Items
                    </a>
                </div>
            </div>
        </li>

        <!-- Nav Item - Restaurant Management -->
        <li class="nav-item">
            <a class="nav-link collapsed" href="#" data-toggle="collapse" data-target="#collapseRestaurant"
                aria-expanded="true" aria-controls="collapseRestaurant">
                <i class="fas fa-utensils"></i>
                <span>Restaurant Management</span>
            </a>
            <div id="collapseRestaurant" class="collapse" aria-labelledby="headingRestaurant" data-parent="#accordionSidebar">
                <div class="bg-white py-2 collapse-inner rounded">
                    <h6 class="collapse-header">Manage Restaurant:</h6>
                    <a class="collapse-item" href="<?php echo e(route('admin.orders')); ?>">
                        <i class="fas fa-hamburger"></i> Orders
                    </a>
                    <a class="collapse-item" href="<?php echo e(route('admin.posinvoices')); ?>">
                        <i class="fas fa-file-invoice"></i>
                        <span>POS Invoices</span>
                    </a>
                    <a class="collapse-item" href="<?php echo e(route('admin.payments')); ?>">
                        <i class="fas fa-money-bill"></i> Payments
                    </a>
                    <a class="collapse-item" href="<?php echo e(route('admin.remittances')); ?>">
                        <i class="fas fa-money-bill-wave"></i> Remittances
                    </a>
    
                </div>
            </div>
        </li>


        <!-- Nav Item - User Management Collapse Menu -->
        <li class="nav-item">
            <a class="nav-link collapsed" href="#" data-toggle="collapse" data-target="#collapseUserManagement"
                aria-expanded="true" aria-controls="collapseUserManagement">
                <i class="fas fa-address-book"></i>
                <span>User Management</span>
            </a>
            <div id="collapseUserManagement" class="collapse" aria-labelledby="headingUserManagement"
                data-parent="#accordionSidebar">
                <div class="bg-white py-2 collapse-inner rounded">
                    <h6 class="collapse-header">Manage Users:</h6>
                    <a class="collapse-item" href="<?php echo e(route('admin.user.add')); ?>">
                        <i class="fas fa-user-plus"></i> Add Users
                    </a>
                    <a class="collapse-item" href="<?php echo e(route('admin.users')); ?>">
                        <i class="fas fa-user"></i> View Users
                    </a>
                    <a class="collapse-item" href="<?php echo e(route('admin.activity-logs')); ?>">
                        <i class="fas fa-file-alt"></i> View Activity Logs
                    </a>
                    <a class="collapse-item" href="<?php echo e(route('admin.guests')); ?>">
                        <i class="fas fa-users"></i> Guests
                    </a>
                    <a class="collapse-item" href="<?php echo e(route('admin.staff')); ?>">
                        <i class="fas fa-users"></i> Staff
                    </a>
                </div>
            </div>
        </li>


        <!-- Nav Item - Subscription -->
        <?php if(auth()->user() && !auth()->user()->isSuperAdmin()): ?>
        <li class="nav-item">
            <a class="nav-link collapsed" href="#" data-toggle="collapse" data-target="#collapseSubscription"
                aria-expanded="true" aria-controls="collapseSubscription">
                <i class="fas fa-fw fa-credit-card"></i>
                <span>Subscription</span>
                <?php if(get_current_hotel() && get_current_hotel()->isOnTrial()): ?>
                    <span class="badge badge-warning badge-sm ml-1">Trial</span>
                <?php endif; ?>
            </a>
            <div id="collapseSubscription" class="collapse" aria-labelledby="headingSubscription"
                data-parent="#accordionSidebar">
                <div class="bg-white py-2 collapse-inner rounded">
                    <h6 class="collapse-header">Subscription:</h6>
                    <a class="collapse-item" href="<?php echo e(route('admin.subscription.current')); ?>">Current Plan</a>
                    <a class="collapse-item" href="<?php echo e(route('admin.subscription.plans')); ?>">Upgrade Plan</a>
                    <a class="collapse-item" href="<?php echo e(route('admin.subscription.billing')); ?>">Billing History</a>
                </div>
            </div>
        </li>
        <?php endif; ?>

        <!-- Nav Item - Settings Collapse Menu -->
        <li class="nav-item">
            <a class="nav-link collapsed" href="#" data-toggle="collapse" data-target="#collapseSettings"
                aria-expanded="true" aria-controls="collapseSettings">
                <i class="fas fa-cogs"></i>
                <span>Settings</span>
            </a>
            <div id="collapseSettings" class="collapse" aria-labelledby="headingSettings"
                data-parent="#accordionSidebar">
                <div class="bg-white py-2 collapse-inner rounded">
                    <h6 class="collapse-header">Settings:</h6>
                    <a class="collapse-item" href="<?php echo e(route('admin.settings.general')); ?>">General Settings</a>
                    <a class="collapse-item" href="<?php echo e(route('admin.settings.payment')); ?>">Payment Settings</a>
                    <?php if(get_current_hotel() && get_current_hotel()->hasFeature('Custom branding')): ?>
                        <a class="collapse-item" href="<?php echo e(route('admin.branding.index')); ?>">Branding</a>
                    <?php endif; ?>
                    <!-- Add more settings links as needed -->
                </div>
            </div>
        </li>




        <!-- Divider -->
        <hr class="sidebar-divider d-none d-md-block">

        <!-- Sidebar Toggler (Sidebar) -->
        <div class="text-center d-none d-md-inline">
            <button class="rounded-circle border-0" id="sidebarToggle"></button>
        </div>

        
        <li class="nav-item">
            <a class="nav-link" href="<?php echo e(url('/')); ?>">
                <i class="fas fa-fw fa-home"></i>
                <span>Home</span></a>
        </li>

        <button id="theme-toggle" class="btn btn-secondary">Toggle Dark Mode</button>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Define the Highcharts dark theme
                const darkTheme = {
                    chart: {
                        backgroundColor: '#343a40',
                        style: {
                            color: '#ffffff'
                        }
                    },
                    title: {
                        style: {
                            color: '#ffffff'
                        }
                    },
                    xAxis: {
                        gridLineColor: '#505050',
                        labels: {
                            style: {
                                color: '#ffffff'
                            }
                        },
                        lineColor: '#ffffff',
                        minorGridLineColor: '#505050',
                        tickColor: '#ffffff',
                        title: {
                            style: {
                                color: '#ffffff'
                            }
                        }
                    },
                    yAxis: {
                        gridLineColor: '#505050',
                        labels: {
                            style: {
                                color: '#ffffff'
                            }
                        },
                        lineColor: '#ffffff',
                        minorGridLineColor: '#505050',
                        tickColor: '#ffffff',
                        tickWidth: 1,
                        title: {
                            style: {
                                color: '#ffffff'
                            }
                        }
                    },
                    legend: {
                        itemStyle: {
                            color: '#ffffff'
                        },
                        itemHoverStyle: {
                            color: '#ffffff'
                        }
                    }
                };

                const lightTheme = {
                    chart: {
                        backgroundColor: '#ffffff',
                        style: {
                            color: '#000000'
                        }
                    },
                    title: {
                        style: {
                            color: '#000000'
                        }
                    },
                    xAxis: {
                        gridLineColor: '#e6e6e6',
                        labels: {
                            style: {
                                color: '#000000'
                            }
                        },
                        lineColor: '#000000',
                        minorGridLineColor: '#e6e6e6',
                        tickColor: '#000000',
                        title: {
                            style: {
                                color: '#000000'
                            }
                        }
                    },
                    yAxis: {
                        gridLineColor: '#e6e6e6',
                        labels: {
                            style: {
                                color: '#000000'
                            }
                        },
                        lineColor: '#000000',
                        minorGridLineColor: '#e6e6e6',
                        tickColor: '#000000',
                        tickWidth: 1,
                        title: {
                            style: {
                                color: '#000000'
                            }
                        }
                    },
                    legend: {
                        itemStyle: {
                            color: '#000000'
                        },
                        itemHoverStyle: {
                            color: '#000000'
                        }
                    }
                };
                

                // Apply the theme based on the current mode
                function applyDarkMode() {
                    Highcharts.setOptions(darkTheme);
                }

                function applyLightMode() {
                    Highcharts.setOptions(lightTheme); // Reset to default
                }

                // Check for saved theme preference
                const savedTheme = localStorage.getItem('theme');
                if (savedTheme == 'dark') {
                    document.documentElement.setAttribute('data-theme', 'dark');
                    applyDarkMode();
                } else {
                    applyLightMode();
                }

                // Toggle theme on button click
                const themeToggle = document.getElementById('theme-toggle');
                themeToggle.addEventListener('click', function() {
                    const currentTheme = document.documentElement.getAttribute('data-theme');
                    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                    document.documentElement.setAttribute('data-theme', newTheme);
                    localStorage.setItem('theme', newTheme);

                    if (newTheme === 'dark') {
                        applyDarkMode();
                    } else {
                        applyLightMode();
                    }

                    // Refresh the charts to apply the new theme
                    refreshcharts();
                    updateChart('year');

                });
            });
        </script>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Get the stored state from local storage
                var hotelFeaturesState = localStorage.getItem('hotelFeaturesState');

                // If the state is saved as 'open', add the 'show' class to keep it open
                if (hotelFeaturesState === 'open') {
                    document.getElementById('collapseHotelFeatures').classList.add('show');
                }

                // Add event listener for the hotel core features collapse toggle
                document.querySelector('[data-target="#collapseHotelFeatures"]').addEventListener('click', function() {
                    // Toggle the state based on whether the collapse is currently showing or hidden
                    var isShown = document.getElementById('collapseHotelFeatures').classList.contains('show');

                    if (isShown) {
                        // If it's open, close it and store the state as 'closed'
                        localStorage.setItem('hotelFeaturesState', 'closed');
                    } else {
                        // If it's closed, open it and store the state as 'open'
                        localStorage.setItem('hotelFeaturesState', 'open');
                    }
                });
            });
        </script>

        <!-- Hotel Switching Script -->
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const hotelSelector = document.getElementById('hotel-selector');
                if (hotelSelector) {
                    hotelSelector.addEventListener('change', function() {
                        const hotelId = this.value;

                        // Create form and submit
                        const form = document.createElement('form');
                        form.method = 'POST';
                        form.action = '<?php echo e(route("admin.switch-hotel")); ?>';

                        const csrfToken = document.createElement('input');
                        csrfToken.type = 'hidden';
                        csrfToken.name = '_token';
                        csrfToken.value = '<?php echo e(csrf_token()); ?>';

                        const hotelInput = document.createElement('input');
                        hotelInput.type = 'hidden';
                        hotelInput.name = 'hotel_id';
                        hotelInput.value = hotelId;

                        form.appendChild(csrfToken);
                        form.appendChild(hotelInput);
                        document.body.appendChild(form);
                        form.submit();
                    });
                }
            });
        </script>

    </ul>
    <!-- End of Sidebar -->

</div>
<!-- End of Page Wrapper -->
<?php /**PATH C:\Users\<USER>\Documents\GitHub\hotel_management_system\resources\views/layout/admin-sidebar.blade.php ENDPATH**/ ?>