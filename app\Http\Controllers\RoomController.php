<?php

namespace App\Http\Controllers;

use App\Models\Room;
use Illuminate\Http\Request;
use App\Models\RoomType;


class RoomController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $rooms = Room::all();
        return view('admin.rooms.view-rooms', compact('rooms'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function add()
    {
        $room_types = RoomType::all();
        return view('admin.rooms.add-rooms', compact('room_types'));
    }


    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'room_number' => 'required | unique:rooms',
            'individual_room_rate' => 'required | numeric',
            'room_type' => 'required | exists:room_types,id',
            'floor' => 'required',
        ]);

        //process the data and store it in the database
        $room = new Room();
        $room->fill($validatedData);
        $room->save();

        //Log the activity
        log_activity('Room added', 'Room Module');


        return redirect()->route('admin.room.add')->with('success', 'Room added successfully');
    }

    /**
     * Display the specified resource.
     */
    public function show(Room $room)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        $room = Room::find($id);
        $room_types = RoomType::all();
        return view('admin.rooms.edit-rooms', compact('room', 'room_types'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $validatedData = $request->validate([
            'room_number' => 'required | unique:rooms,room_number,' . $id,
            'individual_room_rate' => 'required|numeric',
            'room_type' => 'required | exists:room_types,id',
            'floor' => 'required',
        ]);

        //process the data and store it in the database
        $room = Room::find($id);
        $room->fill($validatedData);
        $room->save();

        //Log the activity
        log_activity('Room updated', 'Room Module');

        return redirect()->route('admin.rooms')->with('success', 'Room updated successfully');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Room $room)
    {
        //
    }
}
