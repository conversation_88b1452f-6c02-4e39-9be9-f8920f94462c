# Payment Gateway Configuration for Multi-Tenant Hotels

## Overview

The hotel management system now supports hotel-specific payment gateway configurations. Each hotel can configure their own PayPal and PayMongo API credentials, allowing for proper multi-tenant SaaS functionality.

## Features

- **Hotel-Specific API Keys**: Each hotel can configure their own PayPal and PayMongo credentials
- **Fallback Support**: If no hotel-specific credentials are configured, the system falls back to global .env values
- **Secure Storage**: Payment credentials are stored securely in the database per hotel
- **Easy Configuration**: Simple admin interface for configuring payment settings

## Configuration

### Admin Interface

1. Navigate to **Admin > Settings > Payment Settings**
2. Configure PayPal settings:
   - Client ID
   - Client Secret
   - Mode (sandbox/live)
   - Currency
   - Service Fee Percentage

3. Configure PayMongo settings:
   - Public Key
   - Secret Key
   - Service Fee Percentage

### PayPal Configuration

To get PayPal credentials:
1. Visit [PayPal Developer Dashboard](https://developer.paypal.com/)
2. Create a new application
3. Copy the Client ID and Client Secret
4. Use sandbox credentials for testing, live credentials for production

### PayMongo Configuration

To get PayMongo credentials:
1. Visit [PayMongo Dashboard](https://dashboard.paymongo.com/)
2. Navigate to API Keys section
3. Copy the Public Key and Secret Key
4. Use test keys for testing, live keys for production

## Technical Implementation

### Helper Functions

The system provides several helper functions for retrieving payment configurations:

```php
// Get PayPal configuration for current hotel
$paypalConfig = get_paypal_config();

// Get PayMongo configuration for current hotel
$paymongoConfig = get_paymongo_config();

// Get PayPal access token using hotel-specific credentials
$accessToken = get_paypal_access_token();
```

### Database Structure

Payment settings are stored in the `settings` table with the following keys:

**PayPal Settings:**
- `paypal_client_id`
- `paypal_client_secret`
- `paypal_mode`
- `paypal_currency`
- `paypal_service_fee`

**PayMongo Settings:**
- `paymongo_public_key`
- `paymongo_secret_key`
- `paymongo_service_fee`

### Fallback Behavior

If a hotel doesn't have specific payment credentials configured, the system will fall back to the global environment variables:

- `PAYPAL_CLIENT_ID`
- `PAYPAL_CLIENT_SECRET`
- `PAYPAL_MODE`
- `PAYPAL_CURRENCY`
- `PAYMONGO_PUBLIC_KEY`
- `PAYMONGO_SECRET_KEY`

## Migration

When upgrading to this version, run the migration to add payment gateway settings to existing hotels:

```bash
php artisan migrate
```

This will add empty payment gateway settings to all existing hotels, which can then be configured through the admin interface.

## Testing

The system includes comprehensive tests to verify:
- Hotel-specific payment configurations work correctly
- Fallback to environment variables functions properly
- Payment settings are isolated between hotels
- Admin interface for updating settings works

Run tests with:
```bash
php artisan test tests/Feature/PaymentGatewayConfigTest.php
php artisan test tests/Feature/PaymentSettingsTest.php
```

## Security Considerations

- Payment credentials are stored encrypted in the database
- Each hotel can only access their own payment settings
- Sensitive fields (client secrets, secret keys) are displayed as password fields in the admin interface
- All payment processing uses hotel-specific credentials to ensure proper isolation

## Troubleshooting

### Common Issues

1. **Payment fails with "credentials not configured"**
   - Ensure the hotel has payment credentials configured in Settings > Payment Settings
   - Verify the credentials are correct for the selected mode (sandbox/live)

2. **Settings not saving**
   - Ensure the user has admin access to the current hotel
   - Check that the current hotel is properly set in the session

3. **Payments using wrong credentials**
   - Verify the current hotel context is set correctly
   - Check that the payment controllers are using the helper functions instead of direct env() calls

### Debug Information

To debug payment configuration issues, you can check:

```php
// Current hotel ID
$hotelId = session('current_hotel_id');

// Current hotel's PayPal config
$paypalConfig = get_paypal_config();

// Current hotel's PayMongo config
$paymongoConfig = get_paymongo_config();
```
