<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoices', function (Blueprint $table) {
            $table->id();
            $table->foreignId('booking_id')->nullable()->constrained()->onDelete('set null');
            $table->decimal('total_amount', 10, 2);
            $table->date('invoice_date');
            $table->enum('status', ['unpaid', 'partial', 'paid'])->default('unpaid');
            $table->string('created_by')->nullable();
            $table->string('updated_by')->nullable();
            
            $table->timestamps();
        });


        // Insert sample data
        DB::table('invoices')->insert([
            [
                'booking_id' => 1,
                'total_amount' => 500.00,
                'invoice_date' => Carbon::now()->subDays(5),
                'status' => 'paid',
                'created_by' => 'Admin',
                'updated_by' => 'Admin',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'booking_id' => 2,
                'total_amount' => 250.75,
                'invoice_date' => Carbon::now()->subDays(3),
                'status' => 'paid',
                'created_by' => 'Staff',
                'updated_by' => 'Manager',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'booking_id' => 3,
                'total_amount' => 350.00,
                'invoice_date' => Carbon::now()->subWeek(),
                'status' => 'paid',
                'created_by' => 'Super',
                'updated_by' => 'Super',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'booking_id' => 4,
                'total_amount' => 600.00,
                'invoice_date' => Carbon::now()->subMonth(),
                'status' => 'paid',
                'created_by' => 'Manager',
                'updated_by' => 'Manager',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'booking_id' => 5,
                'total_amount' => 150.00,
                'invoice_date' => Carbon::now()->subDays(10),
                'status' => 'paid',
                'created_by' => 'Super',
                'updated_by' => 'Super',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);       
        
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoices');
    }
};
