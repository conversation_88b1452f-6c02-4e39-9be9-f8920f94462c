<?php

namespace App\Http\Controllers;

use App\Models\RoomType;
use Illuminate\Http\Request;

class RoomTypeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $room_types = RoomType::all();
        return view('admin.roomTypes.view-roomtypes', compact('room_types'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function add()
    {
        return view('admin.roomTypes.add-roomtypes');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required | unique:room_types',
            'description' => 'alpha_num|nullable',//vince orig string
            'rate' => 'required|numeric' //vince added numeric
        ]);

        //process the data and store it in the database
        $room_type = new RoomType();
        $room_type->fill($validatedData);
        $room_type->save();

        //Log the activity
        log_activity('Room Type added', 'Room Type Module');

        return redirect()->route('admin.roomtype.add')->with('success', 'Room Type added successfully');
    }

    /**
     * Display the specified resource.
     */
    public function show(RoomType $roomType)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(RoomType $roomType, $id)
    {
        $room_type = RoomType::find($id);
        return view('admin.roomTypes.edit-roomtypes', compact('room_type'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $validatedData = $request->validate([
            'name' => 'required',
            'description' => 'required',
            'rate' => 'required',
        ]);

        //update roomtype
        $room_type = RoomType::find($id);
        $room_type->fill($validatedData);
        $room_type->save();

        //Log the activity
        log_activity('Room Type updated', 'Room Type Module');

        return redirect()->route('admin.roomtypes')->with('success', 'Room Type updated successfully');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(RoomType $roomType)
    {
        //
    }
}
