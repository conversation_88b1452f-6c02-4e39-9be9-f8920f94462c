@include('layout.admin-header')
@php
    $currencySymbol = config('currency.symbol');
@endphp

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        @include('layout.admin-sidebar')
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                @include('layout.admin-topbar')
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid px-4">
                    <h4 class="mt-4">Grocery List</h4>
                    @include ('shared.error-message')
                    @include ('shared.success-message')
                    @php session()->forget('error'); /*vince added*/ @endphp
{{--vince                     <ol class="breadcrumb mb-4">
                        <li class="breadcrumb-item active">Dashboard</li>
                        <li class="breadcrumb-item">Grocery</li>
                    </ol> --}}
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6>Select Date Range</h6>
                                </div>
                                <div class="card-body">
                                    <form action="{{ route('grocery.search') }}" method="GET">
                                        <div class="row">
                                            <input type="hidden" name="start_date" id="start_date">
                                            <input type="hidden" name="end_date" id="end_date">
                                            <div class="col-md-5">
                                                <label for="start_date">Start Date</label>
                                                <input type="text" class="form-control datepicker1" required
                                                    autocomplete="off">
                                            </div>
                                            <div class="col-md-5">
                                                <label for="end_date">End Date</label>
                                                <input type="text" class="form-control datepicker2" required
                                                    autocomplete="off">
                                            </div>
                                            <button type="submit" class="btn btn-primary mt-3">Search</button>
                                        </div>
                                        
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    {{-- Display Groceries --}}
                    @if (isset($groceries) && is_array($groceries) && count($groceries) > 0)
                        <div class="row mt-4">
                            <div class="col-md-12">
                                {{-- <div class="card"> --}}
                                    {{-- <div class="card-header"> --}}
                                        <h4>Groceries for
                                            {{-- Display Date from Search Query Params --}}
                                            {{ \Carbon\Carbon::parse(Request::get('start_date'))->format('F j, Y') }} -
                                            {{ \Carbon\Carbon::parse(Request::get('end_date'))->format('F j, Y') }}
                                        </h4>
                                    {{-- </div> --}}
                                    <table class="table table-bordered table-striped" id="myRequests2">
                                        <thead>
                                            <tr>
                                                <th>For: Event ID</th>
                                                <th>Ingredient Name</th>
                                                <th>Quantity</th>
                                                <th>Supplier</th>
                                                <th>Cost</th>
                                                <th>Purchased?</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($groceries as $grocery)
                                                <tr>
                                                    <td><?php if (!empty($grocery['event_ids'])): ?>
                                                        <?= implode(', ', $grocery['event_ids']); ?>
                                                    <?php endif; ?>
                                                        {{--vince orig @foreach ($grocery['event_ids'] as $event_id)
                                                            {{ $event_id }}@if (!$loop->last),@endif
                                                        @endforeach --}}
                                                    </td>
                                                    <td>{{ $grocery['ingredient_name'] }}</td>
                                                    <td>{{ $grocery['quantity'] . ' ' . $grocery['unit'] }}</td>
                                                    <td>{{ $grocery['supplier'] }}</td>
                                                    <td>{{ $currencySymbol . number_format($grocery['cost'], 2) }}</td>
                                                    <td>
                                                        <input type="checkbox" class="grocery-checkbox"
                                                            data-ingredient-id="{{ $grocery['ingredient_id'] }}"
                                                            data-event-ids="{{ json_encode($grocery['event_ids']) }}"
                                                            {{ $grocery['is_bought'] ? 'checked' : '' }}>

                                                    </td> <!-- Checkbox for purchased status -->
                                                </tr>
                                            @endforeach
                                        </tbody>
                                        
                                    </table>
                                    <div class="text-right"><h5>Total Cost: {{-- vince edited this div --}}
                                        <span>{{ $currencySymbol . number_format($totalCost, 2) }}</span></h5>
                                    </div>
                                {{-- </div> --}}
                            </div>
                        </div>
                    @endif

                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            @include('layout.admin-footer')
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary" href="login.html">Logout</a>
                </div>
            </div>
        </div>
    </div>

    @include('layout.scripts')

</body>

<script>
    $(document).ready(function() {
        $('.datepicker1').datepicker({
            showOnFocus: true,
            dateFormat: 'mm/dd/yy', // Displayed date format
            altFormat: 'yy-mm-dd', // Format for form submission
            altField: "#start_date" // IDs of hidden input fields to store formatted dates
        });
        <?php if (!isset($groceries)): /*vince added block*/ ?>
            $('.datepicker1').datepicker("setDate", new Date());
        <?php endif; ?>
        $('.datepicker2').datepicker({
            showOnFocus: true,
            dateFormat: 'mm/dd/yy', // Displayed date format
            altFormat: 'yy-mm-dd', // Format for form submission
            altField: "#end_date" // IDs of hidden input fields to store formatted dates
        });
        <?php if (!isset($groceries)): /*vince added block*/ ?>
            $('.datepicker2').datepicker("setDate", new Date());
        <?php endif; ?>
    });

    $(document).ready(function() {
        $('.grocery-checkbox').on('change', function() {
            const ingredientId = $(this).data('ingredient-id');
            const eventIds = $(this).data('event-ids'); // Updated to handle an array of event IDs
            const isChecked = $(this).is(':checked') ? 1 : 0;

            $.ajax({
                url: '{{ route('grocery.update-purchase-status') }}',
                method: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    ingredient_id: ingredientId,
                    event_ids: eventIds, // Send the array of event IDs
                    is_bought: isChecked
                },
                success: function(response) {
                    if (response.success) {
                        alert('Purchase status updated successfully!');
                    } else {
                        alert('An error occurred. Please try again.');
                    }
                },
                error: function(xhr) {
                    alert('An error occurred: ' + xhr.responseText);
                }
            });
        });
    });
</script>


</html>
