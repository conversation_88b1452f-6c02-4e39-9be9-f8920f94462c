<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\BelongsToHotel;

class Ingredient extends Model
{
    use HasFactory, BelongsToHotel;

    protected $table = 'ingredients'; // Specify the custom table name
    protected $fillable = [
        'id',
        'hotel_id',
        'ingredient_name',
        'supplier',
        'unit',
        'cost',
        'minqty',
        'stock',
        'pending_usage',
        'created_at',
        'updated_at',
    ];
}
