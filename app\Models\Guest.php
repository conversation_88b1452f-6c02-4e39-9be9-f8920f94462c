<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\BelongsToHotel;

class Guest extends Model
{
    use HasFactory, BelongsToHotel;

    protected $table = 'guests';
    protected $fillable = ['hotel_id', 'first_name', 'last_name', 'email', 'phone', 'address', 'notes'];

    public function bookings()//vince added function
    {
        return $this->hasMany(Booking::class);
    }
}
