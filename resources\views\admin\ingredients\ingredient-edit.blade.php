@include('layout.admin-header')
{{-- @include('layout.scripts') --}}
@php
    $currencySymbol = config('currency.symbol');
@endphp

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        @include('layout.admin-sidebar')
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                @include('layout.admin-topbar')
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    @include('shared.success-message')
                    @include('shared.error-message')
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Edit Ingredient</h6>
                            <a href="{{ route('admin.ingredients') }}" class="btn btn-danger float-right">BACK</a>
                        </div>
                        <div class="card-body">
                            {{-- Form to Update Ingredient --}}
                            <form action="{{ route('admin.ingredient.update', $ingredient->id) }}" method="POST">
                                @csrf
                            @method('PATCH')
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="ingredient_name">Ingredient Name</label>
                                    <input type="text" id="ingredient_name" name="ingredient_name" value="{{ $ingredient->ingredient_name }}" required class="form-control">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="supplier">Supplier</label>
                                    <input type="text" id="supplier" name="supplier" value="{{ $ingredient->supplier }}" class="form-control">
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="minqty">Minimum Quantity</label>
                                    <input type="number" id="minqty" name="minqty" value="{{ $ingredient->minqty }}" class="form-control">
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="unit">Unit</label>
                                    <input type="text" id="unit" name="unit" value="{{ $ingredient->unit }}" required class="form-control">
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="cost">Cost</label>
                                    <input type="number" id="cost" name="cost" value="{{ $ingredient->cost }}" class="form-control">
                                </div>
                                <div class="form-group col-md-3 mb-3">
                                    <label for="stock">Current Stock</label>
                                    <input type="number" id="stock" name="stock" value="{{ $ingredient->stock }}" class="form-control">
                                </div>
                                <div class="form-check col-md-3 pl-5 pt-4 mb-3">
                                    <input type="checkbox" id="for_pos" name="for_pos" value="1" class="form-check-input">
                                    <label for="for_pos" class="form-check-label">Can be sold (listed on POS)</label>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="price">Price on POS</label>
                                    <input type="number" id="price" name="price" value="0" class="form-control">
                                </div>
                            </div>

                            <div class="col-md-12 mb-3">
                                <button type="submit" name="ingredientEdit" class="btn btn-primary">Update Ingredient</button>
                            </div>

                            </form>

                        </div>
                    </div>
                </div>
            <!-- /.container-fluid -->

        </div>
        <!-- End of Main Content -->

        <!-- Footer -->
        @include('layout.admin-footer')
        <!-- End of Footer -->

    </div>
    <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary" href="login.html">Logout</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap core JavaScript-->
    {{-- <script src="{{ asset('assets/js/jquery.min.js') }}"></script>
    <script src="{{ asset('assets/js/bootstrap.bundle.min.js') }}"></script> --}}


    <!-- Core plugin JavaScript-->
    {{-- <script src="{{ asset('assets/js/jquery.easing.min.js') }}"></script> --}}

    <!-- Custom scripts for all pages-->
    {{-- <script src="{{ asset('assets/js/sb-admin-2.min.js') }}"></script> --}}



    <!--Datetime Picker JS and CSS-->
    {{-- <script src="{{ asset('assets/js/datetimepicker.js') }}"></script> --}}

    @include('layout.scripts')


</body>

</html>
