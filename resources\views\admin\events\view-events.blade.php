@include('layout.admin-header')
@php
$currencySymbol = config('currency.symbol');
@endphp
<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        @include('layout.admin-sidebar')
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                @include('layout.admin-topbar')
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    @include('shared.success-message')
                    @include('shared.error-message')
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Events</h6>
                            <a href="{{ route('admin.event.add') }}" class="btn btn-primary float-right"><i
                                class="fa fa-plus"></i> Add Event</a>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="myRequests" class="table table-bordered table-striped" width="100%"
                                    cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Room</th>
                                            <th>Event Name</th>
                                            <th>Guest</th>
                                            <th>Start Date</th>
                                            <th>End Date</th>
                                            <th>Sub-Total</th>
                                            <th>Created By</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                            
                                        </tr>
                                    </thead>
                                    <!-- Moment -->
                                    <script src="{{ asset('assets/js/moment.min.js') }}"></script>
                                    <tbody>
                                        @foreach ($events as $event)
                                            <tr>
                                                <td>{{ $event->id }}</td>
                                                <td>{{ $event->room->room_number .' - '. $event->room->roomType->name}}</td>
                                                <td>{{ $event->name }}</td>
                                                <td>@if ($event->booking->guest_id)
                                                    <a href="{{ route('admin.guest.edit', $event->booking->guest_id) }}">
                                                        {{ $event->customer_first_name . ' ' . $event->customer_last_name }}
                                                    </a>
                                                    @else
                                                        {{ $event->customer_first_name . ' ' . $event->customer_last_name }}
                                                    @endif
                                                </td>
                                                <td> {{ Carbon\Carbon::parse($event->start_date)->format('F j, Y h:i A') }}</td>
                                                <td>{{ Carbon\Carbon::parse($event->end_date)->format('F j, Y h:i A') }}</td>
                                                <td>{{ $currencySymbol . number_format($event->sub_total, 2) }}</td>
                                                <td>{{ $event->created_by}}</td>
                                                <td>{{ $event->status }}</td>
                                                <td>
                                                    <a href="{{ route('admin.event.edit', $event->id) }}"
                                                        class="btn btn-primary btn-circle btn-sm" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form action="{{ route('admin.event.destroy', $event->id) }}"
                                                        method="POST" style="display: inline;" class="deleteForm">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-danger btn-circle btn-sm deleteButton" title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                    @if ($event->status != 'completed')
                                                        <form action="{{ route('admin.completeEvent', $event->id) }}" method="POST" style="display: inline;">
                                                            @csrf
                                                            @method('POST')
                                                            <button type="submit" class="btn btn-success btn-circle btn-sm" title="Finished!">
                                                                <i class="fas fa-check"></i>
                                                            </button>
                                                        </form>
                                                    @endif
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                               
                            </div>
                        </div>
                    </div>
                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            @include('layout.admin-footer')
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary" href="login.html">Logout</a>
                </div>
            </div>
        </div>
    </div>

    @include('layout.scripts')

</body>

</html>
