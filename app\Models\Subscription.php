<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Subscription extends Model
{
    use HasFactory;

    protected $fillable = [
        'hotel_id',
        'subscription_plan_id',
        'status',
        'trial_ends_at',
        'current_period_start',
        'current_period_end',
        'cancelled_at',
        'stripe_subscription_id',
        'stripe_customer_id',
        'amount',
        'billing_cycle',
        'metadata'
    ];

    protected $casts = [
        'trial_ends_at' => 'datetime',
        'current_period_start' => 'datetime',
        'current_period_end' => 'datetime',
        'cancelled_at' => 'datetime',
        'amount' => 'decimal:2',
        'metadata' => 'array',
    ];

    // Relationships
    public function hotel()
    {
        return $this->belongsTo(Hotel::class);
    }

    public function plan()
    {
        return $this->belongsTo(SubscriptionPlan::class, 'subscription_plan_id');
    }

    public function billingInvoices()
    {
        return $this->hasMany(BillingInvoice::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeTrialing($query)
    {
        return $query->where('status', 'trialing');
    }

    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    public function scopeExpired($query)
    {
        return $query->where('status', 'expired');
    }

    // Helper methods
    public function isActive()
    {
        return $this->status === 'active';
    }

    public function isTrialing()
    {
        return $this->status === 'trialing';
    }

    public function isCancelled()
    {
        return $this->status === 'cancelled';
    }

    public function isExpired()
    {
        return $this->status === 'expired';
    }

    public function isPastDue()
    {
        return $this->status === 'past_due';
    }

    public function onTrial()
    {
        return $this->isTrialing() && $this->trial_ends_at && $this->trial_ends_at->isFuture();
    }

    public function trialExpired()
    {
        return $this->trial_ends_at && $this->trial_ends_at->isPast();
    }

    public function daysLeftOnTrial()
    {
        if (!$this->onTrial()) {
            return 0;
        }
        return $this->trial_ends_at->diffInDays(now());
    }

    public function cancel()
    {
        $this->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
        ]);
    }

    public function resume()
    {
        $this->update([
            'status' => 'active',
            'cancelled_at' => null,
        ]);
    }

    public function renew()
    {
        $nextPeriodStart = $this->current_period_end ?? now();
        $nextPeriodEnd = $this->billing_cycle === 'monthly' 
            ? $nextPeriodStart->addMonth()
            : $nextPeriodStart->addYear();

        $this->update([
            'status' => 'active',
            'current_period_start' => $nextPeriodStart,
            'current_period_end' => $nextPeriodEnd,
        ]);
    }

    public function getFormattedAmountAttribute()
    {
        return '$' . number_format($this->amount, 2);
    }

    public function getNextBillingDateAttribute()
    {
        return $this->current_period_end;
    }

    public function getDaysUntilRenewalAttribute()
    {
        if (!$this->current_period_end) {
            return null;
        }
        return $this->current_period_end->diffInDays(now());
    }
}
