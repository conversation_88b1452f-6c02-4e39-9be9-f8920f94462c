@include('layout.admin-header')

@php
    $currencySymbol = config('currency.symbol');
    

@endphp

<style>
    .disabled-button {
        background-color: #d6d6d6;
        /* Light grey background */
        color: #a0a0a0;
        /* Grey text color */
        cursor: not-allowed;
        /* Show a 'not-allowed' cursor */
        border-color: #b0b0b0;
        /* Grey border */
    }

    .disabled-button:hover {
        background-color: #d6d6d6;
        /* Keep the same color on hover */
    }

    #logout-button:hover {
        background-color: #FF0000; /* Change to your desired hover color */
        color: #000; /* Change to your desired hover text color */
    }
    #logout-button {
        margin-top:5px;
        color:#FFF;
        height:30px;
        background-color:#000;
    }
    
</style>


<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        @include('layout.admin-sidebar')
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">
                {{-- vince added block --}}
                <div class="col-md-12 text-center mb-1 mt-1">
                    <a class="btn btn-info" style="font-size:14px;margin-right:1px;margin-left:5px;padding-top:0px;padding-bottom:0px;" href="{{ route('admin.order.add') }}">POINT OF SALE</a>
                </div>
                <form method="POST" action="{{ route('logout') }}">
                    @csrf
                <span style="position:absolute;right:25px;top:0px;">
                    <button type="submit" id="logout-button" title="Logout"> 
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </span>
                </form>
                {{-- vince end --}}
                <!-- Topbar -->
                {{-- vince @include('layout.admin-topbar') --}}
                <!-- End of Topbar -->
                <!-- Begin Page Content -->
                <div class="container-fluid">
                    @include('shared.success-message')
                    @include('shared.error-message')
                    <div class="col-md-12 mb-1">
                        <input id="searchInput" placeholder="Search menu" oninput="searchMenu()"></input>
                        <button onclick="showSuspendedSales()" class="btn btn-warning" style="font-size:14px;margin-right:1px;padding-top:0px;padding-bottom:0px;">SUSPENDED SALES</button>
                        <button onclick="showOrders()" class="btn btn-success" style="font-size:14px;margin-right:1px;padding-top:0px;padding-bottom:0px;">ORDERS</button>
                        <button onclick="clearRegister()" class="btn btn-danger" style="font-size:14px;margin-right:1px;padding-top:0px;padding-bottom:0px;">CLEAR</button>
                    </div>
                    <div class="card shadow mb-4">
                        <!--vince<div class="card-header py-3 d-flex justify-content-between">-->
                            <!--<h6 class="m-0 font-weight-bold text-primary">New Order</h6>-->
                            <!--vince<a href="{{-- route('admin.orders') --}}" class="btn btn-danger">BACK</a>-->
                        <!--vince</div>-->
                        
                        <div class="card-body row">
                            
                            <!-- Right Column: Receipt View -->
							<!-- Left Column: Menu Items -->
                            <div class="col-md-4" style="background:#003300;color:#FFF;">
								<label>Menu Items</label>
                                <div class="menu-items-grid" style="padding:1px;background:#FF8C00;border-radius:5px;display:flex;flex-wrap:wrap;justify-content:space-evenly;">
                                    <!-- Dynamically populated buttons for each menu item -->
                                    @foreach ($menu_list as $menu)
                                        <div class="card" style="width:7.3rem;margin:2px;">
                                        <button type="button" class="btn btn-warning menu-item-btn" style="color:#000;background:#FF69B4;margin:2px;height:100%;"
                                            onclick="addMenuItem({{ $menu->id }}, '{{ $menu->name }}', {{ $menu->price }})">
                                            {{ $menu->name }} 
                                            <small style="color:#FFF;">{{ $currencySymbol }}{{ number_format($menu->price, 2) }}</small>
                                        </button>
                                        </div>
                                    @endforeach
                                </div>
							</div>
							<div class="col-md-5" style="background:#003300;color:#FFF;">
                                <div id="paymongo_qr_code" style="background:#003300;color:#FFF;"></div>
                                <label>Receipt</label>
                                <div class="receipt-view p-3 border rounded mb-2" id="receipt_view" style="color:#FFF;background:#F0E68C;">
                                    <!-- Receipt Items will appear here -->
                                    <!-- Add a container to hold the QR code -->
                                </div>
                                
                            </div>
                        

                            <div class="col-md-3">
                                <!-- Customer Info and Order Details -->
                                <span class="badge badge-dark">New Sale</span> <span id="clock" class="badge badge-info"></span>
                                <div class="mt-0">
                                    <div class="form-group">
                                        <label for="customer_name">Customer Name</label>
                                        <input class="form-control" id="customer_name" name="customer_name"
                                            value="Walk-in" autocomplete="off">
                                    </div>
                                    <input type="hidden" id="table_name" name="table_name" value="">
                                </div>
								<div class="form-group">
                                    <label for="order_items_total">Total</label>
                                    <input type="text" id="order_items_total" class="form-control" readonly style="font-size:1.2em;color:#000;text-align:center;" autocomplete="off">
                                </div>
								<div style="display: flex; justify-content: space-evenly; flex-wrap: nowrap;">
                                    <button id="onethousand" type="button" class="btn btn-info mb-1" onclick="addTender(1000)" style="font-size:11px;margin-right:1px;">1000</button>
									<button id="fivehundred" type="button" class="btn btn-warning mb-1" onclick="addTender(500)" style="font-size:11px;margin-right:1px;">500</button>
									<button id="twohundred" type="button" class="btn btn-success mb-1" onclick="addTender(200)" style="font-size:11px;margin-right:1px;">200</button>
									<button id="onehundred" type="button" class="btn btn-primary mb-1" onclick="addTender(100)" style="font-size:11px;margin-right:1px;background:indigo;border-color:indigo;">100</button>
									<button id="fifty" type="button" class="btn btn-danger mb-1" onclick="addTender(50)" style="font-size:11px;margin-right:1px;">50</button>
									<button id="twenty" type="button" class="btn btn-dark mb-1" onclick="addTender(20)" style="font-size:11px;margin-right:1px;background:orange;border-color:orange;">20</button>
                                </div>
								<div class="row">
								<div class="col-md-6">
                                <div class="form-group">
                                    <label for="received_amount">Tendered</label>
                                    <input type="number" id="received_amount" class="form-control" min="0"
                                        oninput="calculateChange()" required style="font-size:1.2em;color:#000;text-align:center;" autocomplete="off">
                                </div>
                                </div>
								<div class="col-md-6">
                                <div class="form-group">
                                    <label for="change_amount">Change</label>
                                    <input type="text" id="change_amount" class="form-control" readonly style="font-size:1.2em;color:#000;text-align:center;" autocomplete="off">
                                </div>
								</div>
								</div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="" id="charge_to_room"
                                        onchange="toggleRoomCharge()">
                                    <label class="form-check-label" for="charge_to_room">
                                        Charge to Room
                                    </label>
                                </div>
                                <div class="form-group mt-0" id="room_number_field" style="display:none;">
                                    <!--vince<label for="room_number">Room Number</label>-->
                                    <select id="room_number" class="form-control">
                                        <!-- Room options will be dynamically populated here -->
                                    </select>
                                </div>
								<div class="row" style="margin-top:10px;">
									<div class="col-md-12" style="padding-left:0px;padding-right:0px;">
                                    <button id="submitButton" type="button" class="btn btn-info btn-block"
                                        onclick="submitOrder()" style="margin-bottom:5px;">Pay</button>
									</div>
									<div class="col-md-6" style="padding-left:0px;padding-right:1px;">
                                    {{-- PAYMONGO BUTTON --}}
                                    <button id="paymongoButton" type="button" class="btn btn-secondary btn-block"
                                        onclick="payfood_order()" style="margin-bottom:5px;">Paymongo</button>
									</div>
									<div class="col-md-6" style="padding-left:0px;padding-right:0px;">
                                    <button id="suspendButton" type="button" class="btn btn-warning btn-block"
                                        onclick="suspendShow()" style="margin-bottom:5px;">Suspend</button>
									</div>
                                    <div class="col-md-6" style="padding-left:0px;padding-right:1px;">
                                        <button id="orderButton" type="button" class="btn btn-success btn-block"
                                            onclick="orderFood()" style="margin-bottom:5px;">Order</button>
                                    </div>
                                    <div class="col-md-6" style="padding-left:0px;padding-right:0px;">
                                        <button id="billButton" type="button" class="btn btn-dark btn-block"
                                            onclick="printBill()" style="margin-bottom:5px;">Bill</button>
                                    </div>
								</div>

                            </div>
                        </div>
                    </div>
                </div>
                <!-- End of Main Content -->

                <!-- Footer -->
                @include('layout.admin-footer')
                <!-- End of Footer -->

            </div>
            <!-- End of Content Wrapper -->

        </div>
        <!-- End of Page Wrapper -->

        <!-- Scroll to Top Button-->
        <a class="scroll-to-top rounded" href="#page-top">
            <i class="fas fa-angle-up"></i>
        </a>

        <!-- Suspend Modal-->
        <div class="modal fade" id="suspendModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel">Suspend Sale</h5>
                        <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <label for="notes">Note</label>
                        <input class="form-control" id="note" name="note" autocomplete="off">
                    </div>
                    <div class="modal-footer">
                        <button id="" type="button" class="btn btn-warning"
                            onclick="suspendSales()" style="margin-bottom:5px;">Suspend</button>
                    </div>
                </div>
            </div>
        </div>
        <!-- Suspended Sales-->
        <div class="modal fade" id="suspendedSalesModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel">Suspended Sales</h5>
                        <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        
                    </div>

                </div>
            </div>
        </div>
        <!-- Orders-->
        <div class="modal fade" id="ordersModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel">Sales Orders</h5>
                        <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <label for="notes">Note</label>
                        <input class="form-control" id="note" name="note" autocomplete="off">
                    </div>
                    <div class="modal-footer">
                        <button id="" type="button" class="btn btn-warning"
                            onclick="suspendSales()" style="margin-bottom:5px;">Suspend</button>
                    </div>
                </div>
            </div>
        </div>
        

        <!-- Bootstrap core JavaScript-->
        <script src="{{ asset('assets/js/jquery.min.js') }}"></script>
        <script src="{{ asset('assets/js/bootstrap.bundle.min.js') }}"></script>
        <script src="{{ asset('assets/js/jquery.easing.min.js') }}"></script>
        <script src="{{ asset('assets/js/sb-admin-2.min.js') }}"></script>
        <script src="{{ asset('assets/js/datetimepicker.js') }}"></script>

        <script>
            //vince added block
			$(document).ready(function () {//vince added block
				$("body").toggleClass("sidebar-toggled");
				$(".sidebar").toggleClass("toggled");
				if ($(".sidebar").hasClass("toggled")) {
				  $('.sidebar .collapse').collapse('hide');
				};
                //vince added
                function updateDateTime() {
                    const now = new Date();
                    
                    // Format date as "Oct 31, 2024"
                    const dateOptions = { year: 'numeric', month: 'short', day: 'numeric' };
                    const formattedDate = now.toLocaleDateString('en-US', dateOptions);
                    
                    // Format time as "3:10 PM"
                    const timeOptions = { hour: 'numeric', minute: 'numeric', second: 'numeric', hour12: true };
                    const formattedTime = now.toLocaleTimeString('en-US', timeOptions);
                    $('#clock').text(formattedDate+' '+formattedTime);
                }
                
                // Update the date and time immediately, and then every second
                updateDateTime();
                setInterval(updateDateTime, 1000);

			});
            $('#charge_to_room').prop('checked', false); // Uncheck the checkbox
			//vince end
            let orderItems = [];
			var total = 0;

			//vince added function
			function addTender(bill){
					var tenderedAmount = parseFloat($('#received_amount').val()) || 0;
					$('#received_amount').val(parseFloat(bill)+tenderedAmount);
					calculateChange();
			}
			
            function addMenuItem(id, name, price) {
                const existingItem = orderItems.find(item => item.id === id);
                if (existingItem) {
                    existingItem.quantity++;
                } else {
                    orderItems.push({
                        id,
                        name,
                        price,
                        quantity: 1
                    });
                }
                updateReceiptView();
            }

            function updateReceiptView() {
                const receiptView = document.getElementById('receipt_view');
                receiptView.innerHTML = '';

                total = 0;
                orderItems.forEach((item, index) => {
                    total += item.price * item.quantity;
                    const itemRow = document.createElement('div');
                    itemRow.className = 'row';//vince orig 'd-flex justify-content-between mb-2';

                    itemRow.innerHTML = `
                        <div class="col-md-7" style="margin-bottom:2px;">${item.quantity}x ${item.name}</div>
                        <div class="col-md-3" style="margin-bottom:2px;text-align:right;"><small>${(item.price * item.quantity).toFixed(2)}</small></div>
						<div class="col-md-2" style="margin-bottom:2px;text-align:right;font-size:10px;">
                        <button type="button" class="btn btn-danger btn-sm" onclick="if(event.ctrlKey) { removeMenuItem(${index}); } else { editMenuItem(${index}); }" title="Click to remove one, Ctrl-Click to remove all"><i class="fa fa-trash"></i></button></div>
                    `;<?php //vince added div classes and styles ?>

                    receiptView.appendChild(itemRow);
                });

                document.getElementById('order_items_total').value = total.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });//vince orig total.toFixed(2);
                calculateChange();
            }

            function removeMenuItem(index) {
                orderItems.splice(index, 1);
                updateReceiptView();
            }
            //vince added function
            function editMenuItem(index) {
                //orderItems.splice(index, 1);
                orderItems[index].quantity--;
                if (orderItems[index].quantity <= 0){
                    orderItems.splice(index, 1);
                }
                updateReceiptView();
            }

            function calculateChange() {
                //vince const total = parseFloat(document.getElementById('order_items_total').value) || 0;
                const received = parseFloat(document.getElementById('received_amount').value) || 0;
                const change = received - total;
				var change_display = 0;
				if (change >0) {change_display = change;}
                document.getElementById('change_amount').value = change_display.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });

                // Disable the submit button if the change is less than 0
                const submitButton = document.getElementById('submitButton');
                /*vince if (change < 0) {
                    submitButton.disabled = true;
                    submitButton.classList.add('disabled-button'); // Add CSS class for disabled state
                } else {
                    submitButton.disabled = false;
                    submitButton.classList.remove('disabled-button'); // Remove CSS class
                }*/
            }

            function toggleRoomCharge() {
                const roomChargeChecked = document.getElementById('charge_to_room').checked;
                const roomNumberField = document.getElementById('room_number_field');
                roomNumberField.style.display = roomChargeChecked ? 'block' : 'none';


                if (roomChargeChecked) {
                    $('#submitButton').text('Charge');//vince added
                    fetchRoomOptions();
                } else {
                    $('#submitButton').text('Pay');//vince added
                    const roomNumberSelect = document.getElementById('room_number');
                    roomNumberSelect.innerHTML = ''; // Clear the room options if unchecked
                }
            }
            function resetRoomCharge() {
                $('#charge_to_room').prop('checked', false);
                $('#room_number_field').css('display', 'none'); // Corrected to use .css() for display
                $('#submitButton').text('Pay'); // Update button text
                $('#room_number').html(''); // Use .html() to clear content
            }

            function fetchRoomOptions() {
                $.ajax({
                    url: "{{ route('get.available.rooms.forRestaurant') }}",
                    method: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        startDate: '{{ now()->toDateString() }}'
                    },
                    success: function(response) {
                        const roomNumberSelect = document.getElementById('room_number');
                        roomNumberSelect.innerHTML = '';

                        response.rooms.forEach(room => {
                            const option = document.createElement('option');
                            option.value = room.id;
                            option.text = `${room.number} (${room.guest_name}) `;//vince orig  option.text = `Room ${room.number} (${room.type}) (${room.guest_name}) `;
                            roomNumberSelect.appendChild(option);
                        });
                    },
                    error: function(xhr) {
						console.error('Error fetching booked rooms:', xhr.responseText);
					}

                });
            }

            function payfood_order() {
                const customerName = document.getElementById('customer_name').value;
                const tableName = document.getElementById('table_name').value;//vince added
                const roomCharge = document.getElementById('charge_to_room').checked;
                const roomNumber = document.getElementById('room_number').value;
                const totalAmount = parseFloat(document.getElementById('order_items_total').value) || 0;
                const receivedAmount = parseFloat(document.getElementById('received_amount').value) || 0;
                const changeAmount = parseFloat(document.getElementById('change_amount').value) || 0;

                const items = orderItems.map(item => ({
                    menu_id: item.id,
                    quantity: item.quantity
                }));

                const orderData = {
                    customer_name: customerName,
                    table_name: tableName,//vince added
                    items: items,
                    total_amount: totalAmount,
                    received_amount: receivedAmount,
                    change_amount: changeAmount,
                    room_charge: roomCharge,
                    room_number: roomNumber
                };

                $.ajax({
                    url: "{{ route('admin.payments.paymongo.payfood_order') }}",
                    method: 'POST', // Changed from GET to POST
                    data: {
                        _token: '{{ csrf_token() }}',
                        orderData: JSON.stringify(orderData)
                    },
                    success: function(response) {
                        // Display the QR code
                        console.log(response); // Log the entire response
                        console.log(response.qr_code);
                        $('#paymongo_qr_code').empty().append(`
                <img src="data:image/svg+xml;base64,${response.qr_code}" alt="QR Code" / style="height:300px; width:300px">
                <p>CHECKOUT URL: <a href="${response.redirect_url}" target="_blank">${response.redirect_url}</a></p>
            `);



                    },
                    error: function(xhr) {
                        alert('Error submitting order: ' + xhr.responseText);
                    }
                });
            }


            function submitOrder() {
                if (orderItems.length){
                    const customerName = document.getElementById('customer_name').value;
                    const tableName = document.getElementById('table_name').value;//vince added
                    const roomCharge = document.getElementById('charge_to_room').checked; // Send boolean directly
                    const roomNumber = document.getElementById('room_number').value;
                    const totalAmount = parseFloat(document.getElementById('order_items_total').value) || 0;
                    const receivedAmount = parseFloat(document.getElementById('received_amount').value) || 0;
                    const changeAmount = parseFloat(document.getElementById('change_amount').value) || 0;

                    // Prepare the order items data
                    const items = orderItems.map(item => ({
                        menu_id: item.id,
                        quantity: item.quantity
                    }));

                    // Prepare data to send to server
                    const orderData = {
                        customer_name: customerName,
                        table_name: tableName,//vince added
                        items: items,
                        total_amount: totalAmount,
                        received_amount: receivedAmount,
                        change_amount: changeAmount,
                        room_charge: roomCharge, // Ensure this is a boolean
                        room_number: roomNumber
                    };

                    // Send data to server
                    $.ajax({
                        url: "{{ route('admin.order.store') }}",
                        method: 'POST',
                        data: {
                            _token: '{{ csrf_token() }}',
                            orderData: JSON.stringify(orderData) // Send as JSON
                        },
                        success: function(response) {
                            // Handle successful response (e.g., display a success message or redirect)
                            //vince alert('Order submitted successfully!');
                            const url = "{{ route('admin.posinvoice.print', ':invoice_id') }}".replace(':invoice_id', response.invoice_id);
                            window.location.href = url;
                        },
                        error: function(xhr) {
                            // Handle error response
                            alert('Error submitting order: ' + xhr.responseText);
                        }
                    });
                }
            }

            //vince added block
            function suspendShow(){
                if (orderItems.length) {
                    $('#suspendModal').modal('show');
                    $('#note').val("");
                }
                
            }

            function suspendSales(){

                var note = $('#suspendModal #note').val();
                $('#suspendModal').modal('hide');
                // Prepare new order
                var customerName = $('#customer_name').val();
                var itemCount = 0;//orderItems.length;
                orderItems.forEach((orderedItems)=>{
                    itemCount += orderedItems.quantity;
                })
                var note = $('#note').val();
                var date_suspended = new Date();
                var newOrder = { "customer": customerName, "items": itemCount, "note": note, "dateSuspended":date_suspended, "ordered": orderItems };

                // Get existing orders from localStorage
                var existingOrders = localStorage.getItem("suspendedOrders");
                var existingOrdersArray = existingOrders ? JSON.parse(existingOrders) : [];

                // Add the new order to the existing orders array
                existingOrdersArray.push(newOrder);
                
                // Save the updated array back to localStorage
                localStorage.setItem("suspendedOrders", JSON.stringify(existingOrdersArray));
                clearRegister();
            }

            function showSuspendedSales(){
                $('#suspendedSalesModal').modal('show');
                const modalBody = $('#suspendedSalesModal .modal-body');
                modalBody.empty(); // Clear existing content in the modal body

                var existingOrders = localStorage.getItem("suspendedOrders");
                var existingOrdersArray = existingOrders ? JSON.parse(existingOrders) : [];
                var number_existing_orders = 0;
                existingOrdersArray.forEach((order, i) => {
                    number_existing_orders++;
                    const cust_name = order.customer;
                    const no_of_items = order.items>1? order.items + " items": order.items+ " item";
                    const note = order.note;
                    const date_suspended = new Date(order.dateSuspended);

                    // Format the date to a default string but exclude seconds
                    const formattedDate = date_suspended.toLocaleString('en-US', {
                        hour12: true,
                        hour: 'numeric',
                        minute: 'numeric',
                        //second: '2-digit',
                        day: '2-digit',
                        month: '2-digit',
                        year: 'numeric',
                    });

                    // Remove the seconds part from the formatted date string
                    const formattedDateWithoutSeconds = formattedDate.replace(/:\d{2}$/, '');

                    const card = `
                        <div class="card mb-3">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <span class="badge badge-success" style="border-radius:50%;">${i + 1}: ${note}</span> 
                                    <span class="badge badge-primary">${formattedDateWithoutSeconds}</span>
                                    <span class="badge badge-warning">${no_of_items}</span>
                                    <span style="float:right;">
                                        <button class="btn btn-primary" onclick="resumeSale(${i})">Resume</button>
                                    </span>
                                </h6>
                                <p class="card-text">Customer: ${cust_name}</p>
                            </div>
                        </div>
                    `;
                    
                    modalBody.append(card);
                });
                if (number_existing_orders == 0){
                    modalBody.append("<span>No Suspended Sales</span>");
                }
            }
            
            function clearRegister(){
                resetRoomCharge();
                orderItems.splice(0, orderItems.length);//empty the array
                updateReceiptView();
                $('#customer_name').val("Walk-in");
            }

            function resumeSale(saleIndex){
                $('#suspendedSalesModal').modal('hide');
                var existingOrders = localStorage.getItem("suspendedOrders");
                var existingOrdersArray = existingOrders ? JSON.parse(existingOrders) : [];
                if (existingOrdersArray.length > 0){
                    var orderToResume = existingOrdersArray[saleIndex];
                    clearRegister();
                    orderToResume.ordered.forEach((orderedItem)=>{
                        orderItems.push({
                            id: orderedItem.id,
                            name: orderedItem.name,
                            price: orderedItem.price,
                            quantity: orderedItem.quantity
                        });
                    })
                    updateReceiptView();
                    $('#customer_name').val(orderToResume['customer']);
                    existingOrdersArray.splice(saleIndex, 1);
                    
                    // Save the updated array back to localStorage
                    localStorage.setItem("suspendedOrders", JSON.stringify(existingOrdersArray));
                }
            }

            function showOrders(){
                $('#ordersModal').modal('show');
            }

            function searchMenu(){
                searchTerm = $('#searchInput').val();
                if (!searchTerm) { // Check if searchTerm is empty or null
                    $('.menu-items-grid .card').show(); // Show all cards if search term is empty
                    return;
                }
                $('.menu-items-grid .card').each(function() {
                    const button = $(this).find('.menu-item-btn'); // Find the button inside the card
                    const menuName = button.text().trim(); // Get the button text and trim whitespace
                    
                    // Check if the menu name includes the search term (case-insensitive)
                    if (menuName.toLowerCase().includes(searchTerm.toLowerCase())) {
                        $(this).show(); // Show the card if it contains the search term
                    } else {
                        $(this).hide(); // Hide the card if it doesn't contain the search term
                    }
                });
            }
            //vince end
        </script>

</body>

</html>
