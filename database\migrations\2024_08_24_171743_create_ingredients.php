<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ingredients', function (Blueprint $table) {
            $table->id();
            $table->string('ingredient_name');
            $table->string('supplier')->nullable();
            //vince $table->float('quantity')->nullable();
            $table->string('unit');//vince orig nullable
            $table->string('cost')->default('0');//vince orig nullable
            $table->decimal('minqty', 8, 2)->default(0);//vince added
            $table->decimal('stock', 8, 2)->default(0);//vince orig $table->float('stock')->nullable();
            $table->decimal('pending_usage', 8, 2)->default(0);//vince orig $table->float('pending_usage')->default(0);
            $table->timestamps();
        });

        // Insert some stuff
        DB::table('ingredients')->insert([
            [
                'ingredient_name' => 'Flour',
                'supplier' => 'Supplier 1',
                'unit' => 'kg',
                'cost' => '5.00',
                'minqty' => 5, //vince added
                'stock' => 10,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'ingredient_name' => 'Sugar',
                'supplier' => 'Supplier 2',
                'unit' => 'kg',
                'cost' => '5.00',
                'minqty' => 3, //vince added
                'stock' => 9,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'ingredient_name' => 'Salt',
                'supplier' => 'Supplier 3',
                'unit' => 'g',
                'cost' => '1.00',
                'minqty' => 2, //vince added
                'stock' => 7,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'ingredient_name' => 'Butter',
                'supplier' => 'Supplier 4',
                'unit' => 'block',
                'cost' => '250.00',
                'minqty' => 5, //vince added
                'stock' => 8,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'ingredient_name' => 'Egg',
                'supplier' => 'Supplier 5',
                'unit' => 'pc',
                'minqty' => 300, //vince added
                'cost' => '5.00',
                'stock' => 6,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'ingredient_name' => 'Spaghetti Noodles',
                'supplier' => 'Savemore',
                'unit' => 'pack',
                'cost' => '55.00',
                'minqty' => 10, //vince added
                'stock' => 3,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ingredients');
    }
};
