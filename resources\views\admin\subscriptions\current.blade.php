@include('layout.admin-header')

<body id="page-top">
    <!-- Page Wrapper -->
    <div id="wrapper" data-bs-theme="light">
        <!-- Sidebar -->
        @include('layout.admin-sidebar')
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">
            <!-- Main Content -->
            <div id="content">
                <!-- Topbar -->
                @include('layout.admin-topbar')
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    @include('shared.success-message')
                    @include('shared.error-message')

                    <!-- Page Heading -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">Current Subscription</h1>
                        <div>
                            <a href="{{ route('admin.subscription.plans') }}" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
                                <i class="fas fa-eye fa-sm text-white-50"></i> View Plans
                            </a>
                            <a href="{{ route('admin.subscription.billing') }}" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
                                <i class="fas fa-file-invoice fa-sm text-white-50"></i> Billing History
                            </a>
                        </div>
                    </div>

                    @if($subscription)
                    <!-- Current Subscription Details -->
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                                    <h6 class="m-0 font-weight-bold text-primary">Subscription Details</h6>
                                    <div class="dropdown no-arrow">
                                        <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                                        </a>
                                        <div class="dropdown-menu dropdown-menu-right shadow" aria-labelledby="dropdownMenuLink">
                                            @if($subscription->status === 'active')
                                                <form action="{{ route('admin.subscription.cancel') }}" method="POST" style="display: inline;">
                                                    @csrf
                                                    <button type="submit" class="dropdown-item" onclick="return confirm('Are you sure you want to cancel your subscription?')">
                                                        <i class="fas fa-times fa-sm fa-fw mr-2 text-gray-400"></i>
                                                        Cancel Subscription
                                                    </button>
                                                </form>
                                            @elseif($subscription->status === 'cancelled')
                                                <form action="{{ route('admin.subscription.resume') }}" method="POST" style="display: inline;">
                                                    @csrf
                                                    <button type="submit" class="dropdown-item">
                                                        <i class="fas fa-play fa-sm fa-fw mr-2 text-gray-400"></i>
                                                        Resume Subscription
                                                    </button>
                                                </form>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h5 class="text-primary">{{ $subscription->plan->name }}</h5>
                                            <p class="text-muted">{{ $subscription->plan->description }}</p>

                                            <div class="mb-3">
                                                <strong>Status:</strong>
                                                @if($subscription->status === 'active')
                                                    <span class="badge badge-success">Active</span>
                                                @elseif($subscription->status === 'cancelled')
                                                    <span class="badge badge-warning">Cancelled</span>
                                                @elseif($subscription->status === 'expired')
                                                    <span class="badge badge-danger">Expired</span>
                                                @else
                                                    <span class="badge badge-secondary">{{ ucfirst($subscription->status) }}</span>
                                                @endif
                                            </div>

                                            <div class="mb-3">
                                                <strong>Billing Cycle:</strong> {{ ucfirst($subscription->billing_cycle) }}
                                            </div>

                                            <div class="mb-3">
                                                <strong>Amount:</strong> ${{ number_format($subscription->amount, 2) }}
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <strong>Current Period:</strong><br>
                                                <small class="text-muted">
                                                    {{ $subscription->current_period_start->format('M d, Y') }} -
                                                    {{ $subscription->current_period_end->format('M d, Y') }}
                                                </small>
                                            </div>

                                            @if($subscription->trial_ends_at)
                                            <div class="mb-3">
                                                <strong>Trial Period:</strong><br>
                                                <small class="text-muted">
                                                    @if($subscription->onTrial())
                                                        <span class="badge badge-info">{{ $subscription->daysLeftOnTrial() }} days left</span>
                                                    @else
                                                        Trial ended on {{ $subscription->trial_ends_at->format('M d, Y') }}
                                                    @endif
                                                </small>
                                            </div>
                                            @endif

                                            @if($subscription->status === 'cancelled')
                                            <div class="mb-3">
                                                <strong>Cancellation:</strong><br>
                                                <small class="text-muted">
                                                    Service continues until {{ $subscription->current_period_end->format('M d, Y') }}
                                                </small>
                                            </div>
                                            @endif
                                        </div>
                                    </div>

                                    <!-- Plan Features -->
                                    <hr>
                                    <h6 class="font-weight-bold text-primary">Plan Features</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <ul class="list-unstyled">
                                                @foreach($subscription->plan->features as $index => $feature)
                                                    @if($index < ceil(count($subscription->plan->features) / 2))
                                                    <li class="mb-2">
                                                        <i class="fas fa-check text-success"></i> {{ $feature }}
                                                    </li>
                                                    @endif
                                                @endforeach
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <ul class="list-unstyled">
                                                @foreach($subscription->plan->features as $index => $feature)
                                                    @if($index >= ceil(count($subscription->plan->features) / 2))
                                                    <li class="mb-2">
                                                        <i class="fas fa-check text-success"></i> {{ $feature }}
                                                    </li>
                                                    @endif
                                                @endforeach
                                            </ul>
                                        </div>
                                    </div>

                                    <!-- Usage Stats -->
                                    <hr>
                                    <h6 class="font-weight-bold text-primary">Current Usage</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <strong>Rooms:</strong>
                                                {{ $currentHotel->rooms()->count() }}
                                                @if($subscription->plan->max_rooms)
                                                    / {{ $subscription->plan->max_rooms }}
                                                @else
                                                    (Unlimited)
                                                @endif
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <strong>Users:</strong>
                                                {{ $currentHotel->users()->count() }}
                                                @if($subscription->plan->max_users)
                                                    / {{ $subscription->plan->max_users }}
                                                @else
                                                    (Unlimited)
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="col-lg-4">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                                </div>
                                <div class="card-body">
                                    <a href="{{ route('admin.subscription.plans') }}" class="btn btn-primary btn-block mb-3">
                                        <i class="fas fa-arrow-up"></i> Upgrade Plan
                                    </a>

                                    <a href="{{ route('admin.subscription.billing') }}" class="btn btn-secondary btn-block mb-3">
                                        <i class="fas fa-file-invoice"></i> View Billing
                                    </a>

                                    @if($subscription->status === 'active')
                                        <form action="{{ route('admin.subscription.cancel') }}" method="POST">
                                            @csrf
                                            <button type="submit" class="btn btn-warning btn-block" onclick="return confirm('Are you sure you want to cancel your subscription?')">
                                                <i class="fas fa-times"></i> Cancel Subscription
                                            </button>
                                        </form>
                                    @elseif($subscription->status === 'cancelled')
                                        <form action="{{ route('admin.subscription.resume') }}" method="POST">
                                            @csrf
                                            <button type="submit" class="btn btn-success btn-block">
                                                <i class="fas fa-play"></i> Resume Subscription
                                            </button>
                                        </form>
                                    @endif
                                </div>
                            </div>

                            <!-- Next Billing -->
                            @if($subscription->status === 'active')
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">Next Billing</h6>
                                </div>
                                <div class="card-body">
                                    <div class="text-center">
                                        <h4 class="text-primary">${{ number_format($subscription->amount, 2) }}</h4>
                                        <p class="text-muted">
                                            Due on {{ $subscription->current_period_end->format('M d, Y') }}
                                        </p>
                                        <small class="text-muted">
                                            {{ $subscription->current_period_end->diffForHumans() }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- Recent Invoices -->
                    @if($invoices->count() > 0)
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Recent Invoices</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Invoice #</th>
                                            <th>Date</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($invoices as $invoice)
                                        <tr>
                                            <td>#{{ $invoice->id }}</td>
                                            <td>{{ $invoice->invoice_date->format('M d, Y') }}</td>
                                            <td>${{ number_format($invoice->total_amount, 2) }}</td>
                                            <td>
                                                @if($invoice->status === 'paid')
                                                    <span class="badge badge-success">Paid</span>
                                                @elseif($invoice->status === 'pending')
                                                    <span class="badge badge-warning">Pending</span>
                                                @elseif($invoice->status === 'overdue')
                                                    <span class="badge badge-danger">Overdue</span>
                                                @else
                                                    <span class="badge badge-secondary">{{ ucfirst($invoice->status) }}</span>
                                                @endif
                                            </td>
                                            <td>
                                                <a href="{{ route('admin.subscription.invoice.download', $invoice) }}" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-download"></i> Download
                                                </a>
                                            </td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-center mt-3">
                                <a href="{{ route('admin.subscription.billing') }}" class="btn btn-secondary">
                                    View All Invoices
                                </a>
                            </div>
                        </div>
                    </div>
                    @endif

                    @else
                    <!-- No Subscription -->
                    <div class="card shadow mb-4">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-credit-card fa-3x text-gray-300 mb-4"></i>
                            <h4 class="text-gray-600">No Active Subscription</h4>
                            <p class="text-muted mb-4">You don't have an active subscription. Choose a plan to get started.</p>
                            <a href="{{ route('admin.subscription.plans') }}" class="btn btn-primary">
                                <i class="fas fa-eye"></i> View Subscription Plans
                            </a>
                        </div>
                    </div>
                    @endif
                </div>
                <!-- /.container-fluid -->
            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            @include('layout.admin-footer')
            <!-- End of Footer -->
        </div>
        <!-- End of Content Wrapper -->
    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Include Scripts -->
    @include('layout.scripts')
</body>