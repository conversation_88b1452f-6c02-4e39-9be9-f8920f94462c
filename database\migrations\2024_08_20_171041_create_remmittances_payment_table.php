<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('remmittances_payment', function (Blueprint $table) {
            $table->id();
            $table->string('payment_id'); // Reference to the payment being remitted
            $table->string('remittance_id'); // Reference to the remittance
            $table->string('from_user_email'); // The user remitting the payment (e.g., manager)
            $table->string('to_user_email')->nullable(); // The recipient of the remittance (owner or bank)
            $table->decimal('amount', 10, 2); // Amount being remitted
            $table->string('location'); // Where the cash is currently (e.g., manager, bank, office)
            $table->date('remittance_date'); // Date of the remittance
            $table->string('confirmed_by')->nullable(); // Email of user who confirmed the remittance
            $table->string('status')->nullable(); // Status of the remittance (e.g., pending, confirmed, rejected)
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('remmittances_payment');
    }
};
