<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Assuming your User model has a 'role' attribute, Anyone whose role is not 'user' is an admin
        if (Auth::check() && Auth::user()->role !== 'user') {
            return $next($request);
        }

        // If user is not an admin, redirect or show a 403 page
        return redirect('/dashboard')->with('error', 'You do not have admin access.');
    }
}
