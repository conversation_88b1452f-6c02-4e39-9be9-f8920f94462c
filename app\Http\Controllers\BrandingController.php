<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class BrandingController extends Controller
{
    /**
     * Show branding settings form.
     */
    public function index()
    {
        $currentHotel = get_current_hotel();
        
        if (!$currentHotel) {
            return redirect()->route('unauthorized');
        }

        // Check if hotel has branding feature
        if (!$currentHotel->hasFeature('Custom branding')) {
            return redirect()->route('admin.subscription.plans')
                ->with('error', 'Custom branding is not available in your current plan. Please upgrade to access this feature.');
        }

        return view('admin.branding.index', compact('currentHotel'));
    }

    /**
     * Update branding settings.
     */
    public function update(Request $request)
    {
        $currentHotel = get_current_hotel();
        
        if (!$currentHotel) {
            return redirect()->route('unauthorized');
        }

        if (!$currentHotel->hasFeature('Custom branding')) {
            return redirect()->route('admin.subscription.plans')
                ->with('error', 'Custom branding is not available in your current plan.');
        }

        $request->validate([
            'brand_color' => 'nullable|regex:/^#[a-fA-F0-9]{6}$/',
            'brand_logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'subdomain' => 'nullable|string|max:50|regex:/^[a-z0-9-]+$/|unique:hotels,subdomain,' . $currentHotel->id,
            'custom_domain' => 'nullable|string|max:255|unique:hotels,custom_domain,' . $currentHotel->id,
            'theme_settings.header_style' => 'nullable|in:default,minimal,bold',
            'theme_settings.sidebar_style' => 'nullable|in:light,dark,colored',
            'theme_settings.font_family' => 'nullable|in:default,roboto,open-sans,lato,montserrat',
        ]);

        $updateData = [];

        // Handle brand color
        if ($request->filled('brand_color')) {
            $updateData['brand_color'] = $request->brand_color;
        }

        // Handle logo upload
        if ($request->hasFile('brand_logo')) {
            // Delete old logo if exists
            if ($currentHotel->brand_logo) {
                Storage::disk('public')->delete($currentHotel->brand_logo);
            }

            $logoPath = $request->file('brand_logo')->store('hotel-logos', 'public');
            $updateData['brand_logo'] = $logoPath;
        }

        // Handle subdomain
        if ($request->filled('subdomain')) {
            $subdomain = Str::slug($request->subdomain);
            
            // Check if subdomain is available
            if ($this->isSubdomainAvailable($subdomain, $currentHotel->id)) {
                $updateData['subdomain'] = $subdomain;
            } else {
                return back()->withErrors(['subdomain' => 'This subdomain is already taken.']);
            }
        }

        // Handle custom domain
        if ($request->filled('custom_domain')) {
            $domain = strtolower(trim($request->custom_domain));
            
            // Basic domain validation
            if (!$this->isValidDomain($domain)) {
                return back()->withErrors(['custom_domain' => 'Please enter a valid domain name.']);
            }
            
            $updateData['custom_domain'] = $domain;
        }

        // Handle theme settings
        $themeSettings = $currentHotel->theme_settings ?? [];
        if ($request->has('theme_settings')) {
            $themeSettings = array_merge($themeSettings, $request->theme_settings);
            $updateData['theme_settings'] = $themeSettings;
        }

        // Update hotel
        $currentHotel->update($updateData);

        log_activity('Updated branding settings', 'Branding');

        return back()->with('success', 'Branding settings updated successfully!');
    }

    /**
     * Reset branding to defaults.
     */
    public function reset()
    {
        $currentHotel = get_current_hotel();
        
        if (!$currentHotel) {
            return redirect()->route('unauthorized');
        }

        // Delete logo if exists
        if ($currentHotel->brand_logo) {
            Storage::disk('public')->delete($currentHotel->brand_logo);
        }

        $currentHotel->update([
            'brand_color' => null,
            'brand_logo' => null,
            'theme_settings' => null,
        ]);

        log_activity('Reset branding to defaults', 'Branding');

        return back()->with('success', 'Branding reset to defaults successfully!');
    }

    /**
     * Preview branding changes.
     */
    public function preview(Request $request)
    {
        $currentHotel = get_current_hotel();
        
        if (!$currentHotel) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $previewData = [
            'brand_color' => $request->brand_color ?: $currentHotel->brand_color,
            'theme_settings' => array_merge(
                $currentHotel->theme_settings ?? [],
                $request->theme_settings ?? []
            ),
        ];

        return response()->json($previewData);
    }

    /**
     * Check if subdomain is available.
     */
    private function isSubdomainAvailable($subdomain, $excludeHotelId = null)
    {
        $query = \App\Models\Hotel::where('subdomain', $subdomain);
        
        if ($excludeHotelId) {
            $query->where('id', '!=', $excludeHotelId);
        }
        
        return !$query->exists();
    }

    /**
     * Validate domain format.
     */
    private function isValidDomain($domain)
    {
        return filter_var('http://' . $domain, FILTER_VALIDATE_URL) !== false;
    }

    /**
     * Get available subdomains suggestions.
     */
    public function suggestSubdomains(Request $request)
    {
        $base = Str::slug($request->name ?: 'hotel');
        $suggestions = [];
        
        for ($i = 0; $i < 5; $i++) {
            $suggestion = $i === 0 ? $base : $base . '-' . $i;
            
            if ($this->isSubdomainAvailable($suggestion)) {
                $suggestions[] = $suggestion;
            }
        }
        
        return response()->json($suggestions);
    }
}
