@include('layout.admin-header')
@php
$currencySymbol = config('currency.symbol');
@endphp
<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        @include('layout.admin-sidebar')
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                @include('layout.admin-topbar')
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    @include('shared.success-message')
                    @include('shared.error-message')
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            {{-- If current stock is greater than pending usage, give warning card to buy groceries --}}
                            @php
                            $lowStockIngredients = $ingredients_list->filter(function($ingredient) {
                                return $ingredient->stock < $ingredient->pending_usage;
                            });
                            @endphp
                            @if ($lowStockIngredients->count() > 0)
                                <div class="alert alert-warning" role="alert">
                                    <h4 class="alert-heading">Alert</h4>
                                    <p>The following items have current stock less than pending usage for upcoming events/catering</p>
                                    {{--vince <p>Please Buy Groceries For:</p> --}}
                                    <ul>
                                        @foreach ($lowStockIngredients as $ingredient)
                                            <li>{{ $ingredient->ingredient_name }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif
                            <h6 class="m-0 font-weight-bold text-primary">Items</h6>
                            <a href="{{route('admin.ingredient.add')}}" class="btn btn-primary float-right"><i class="fa fa-plus"></i> Add Item</a>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="myRequests" class="table table-bordered table-striped" width="100%"
                                    cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Item Name</th>
                                            <th>Supplier</th>
                                            <th>Cost</th>
                                            <th>Min Qty</th>
                                            <th>Current Stock</th>
                                            <th>Pending Usage</th>
                                            <th>(Deficit)/Surplus</th>
                                            <th>Action</th>
                                            
                                            
                                        </tr>
                                    </thead>
                                    <!-- Moment -->
                                    <script src="{{ asset('assets/js/moment.min.js') }}"></script>
                                    <tbody>
                                        @foreach ($ingredients_list as $ingredient)
                                            @php
                                                $buffer = $ingredient->stock - $ingredient->minqty;
                                                $pending = $ingredient->pending_usage > $ingredient->stock? true: false;
                                                $needed = -$ingredient->pending_usage + $ingredient->stock - $ingredient->minqty;
                                            @endphp
                                            <tr>
                                                <td>{{ $ingredient->id }}</td>
                                                <td>{{ $ingredient->ingredient_name }}</td>
                                                <td>{{ $ingredient->supplier }}</td>
                                                <td class="text-right">{{ $currencySymbol }}{{ $ingredient->cost }}</td>
                                                <td>{{ $ingredient->minqty.' '.$ingredient->unit}}</td>
                                                <td @php if ($buffer <=0) { echo 'class="table-danger"'; } @endphp>{{ $ingredient->stock  .' '. $ingredient->unit}}</td>
                                                <td @php if ($pending==true) { echo 'class="table-warning"'; } @endphp>{{ $ingredient->pending_usage .' '. $ingredient->unit }}</td>
                                                <td>{{ $needed .' '. $ingredient->unit }}</td>
                                                <td>
                                                    <a href="{{ route('admin.ingredient.edit', $ingredient->id) }}"
                                                        class="btn btn-primary btn-circle btn-sm">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form action="{{ route('admin.ingredient.destroy', $ingredient->id) }}"
                                                        method="POST" style="display: inline;" class="deleteForm">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-danger btn-circle btn-sm deleteButton">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                               
                            </div>
                        </div>
                    </div>
                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            @include('layout.admin-footer')
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary" href="login.html">Logout</a>
                </div>
            </div>
        </div>
    </div>

    @include('layout.scripts')

</body>

</html>
