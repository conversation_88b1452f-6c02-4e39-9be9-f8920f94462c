<?php

namespace App\Traits;

use App\Models\Hotel;
use Illuminate\Database\Eloquent\Builder;

trait BelongsToHotel
{
    /**
     * Boot the trait.
     */
    protected static function bootBelongsToHotel()
    {
        // Automatically scope queries to the current hotel
        static::addGlobalScope('hotel', function (Builder $builder) {
            if (session()->has('current_hotel_id')) {
                $builder->where('hotel_id', session('current_hotel_id'));
            }
        });

        // Automatically set hotel_id when creating new records
        static::creating(function ($model) {
            if (empty($model->hotel_id) && session()->has('current_hotel_id')) {
                $model->hotel_id = session('current_hotel_id');
            }
        });
    }

    /**
     * Get the hotel that owns the model.
     */
    public function hotel()
    {
        return $this->belongsTo(Hotel::class);
    }

    /**
     * Scope a query to only include models for a specific hotel.
     */
    public function scopeForHotel($query, $hotelId)
    {
        return $query->where('hotel_id', $hotelId);
    }

    /**
     * Scope a query to exclude the hotel scope.
     */
    public function scopeWithoutHotelScope($query)
    {
        return $query->withoutGlobalScope('hotel');
    }
}
