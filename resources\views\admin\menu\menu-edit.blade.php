@include('layout.admin-header')
{{-- @include('layout.scripts') --}}
@php
    $currencySymbol = config('currency.symbol');
@endphp

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        @include('layout.admin-sidebar')
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                @include('layout.admin-topbar')
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    @include('shared.success-message')
                    @include('shared.error-message')
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Edit Menu</h6>
                        </div>
                        <div class="card-body">
                            {{-- Form to Update Menu --}}
                            <form action="{{ route('admin.menu.update', $menu->id) }}" method="POST">
                                @csrf
                                @method('PATCH')
                                <div class="row">

                                    <div class="col-md-6 mb-3">
                                        <label for="name">Menu Item Name</label>
                                        <input type="text" id="name" name="name" value="{{ $menu->name }}" required
                                            class="form-control">
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label for="price">Price</label>
                                        <input type="number" id="price" name="price" value="{{ $menu->price }}" id="subtotal"
                                            required class="form-control text-center">
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label for="">Cook Time</label>
                                        <input type="text" name="cook_time" value="{{ $menu->cook_time }}" 
                                            class="form-control">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="">Description</label>
                                        <textarea name="description" class="form-control">{{ $menu->description }}</textarea>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="">How to Cook?</label>
                                        <textarea name="how_to_cook" class="form-control">{{ $menu->how_to_cook }}</textarea>
                                    </div>
                                    
                                </div>

                                <div class="col-md-12 mb-3">
                                    <button type="button" class="btn btn-success" onclick="addMenuItem()">Add
                                        Ingredient *</button>
                                    
                                    <div id="menu_items_container">
                                        <!-- Existing menu items will be loaded here -->
                                        @foreach ($menu->ingredients as $index => $ingredient)

                                            <div class="ingredient-item row mb-3" id="menu_ingredient_{{ $index + 1 }}">
                                                <div class="col-md-4">
                                                    <label>Item Name</label>
                                                    <select name="menu_ingredients[{{ $index + 1 }}][menu_id]"
                                                        class="form-control" required>
                                                        @foreach ($ingredient_list as $ingredientOption)
                                                            <option value="{{ $ingredientOption->id }}" data-cost="{{$ingredientOption->cost}}" data-unit="{{$ingredientOption->unit}}"
                                                                {{ $ingredientOption->id == $ingredient->pivot->ingredient_id ? 'selected' : '' }}>
                                                                {{ $ingredientOption->ingredient_name }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                                <div class="col-md-2">
                                                    <label>Quantity</label>
                                                    <input type="number"
                                                        name="menu_ingredients[{{ $index + 1 }}][quantity]"
                                                        value="{{ $ingredient->pivot->quantity }}" class="form-control item-quantity text-center"
                                                        required min="0" step="any">
                                                </div>
                                                <div class="col-md-2">
                                                    <label>Unit</label>
                                                    <input type="text" name="menu_ingredients[{{ $index + 1 }}][unit]"
                                                        value="{{ $ingredient->pivot->unit }}" class="form-control item-unit"
                                                        required readonly>
                                                </div>
                                                <div class="col-md-2">
                                                    <label>Cost</label>
                                                    <input type="text" name="menu_ingredients[{{ $index + 1 }}][cost]"
                                                        value="" class="form-control item-cost text-right"
                                                        required readonly>
                                                </div>
                                                <div class="col-md-2 d-flex align-items-end">
                                                    <button type="button" class="btn btn-danger" title="Remove"
                                                        onclick="removeIngredient({{ $index + 1 }})"><i class="fa fa-trash"></i></button>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                    
                                </div>
                                <div class="row">
                                    <div class="col-md-6"></div>
                                    <div class="col-md-2 text-center">
                                        Total Cost:
                                    </div>
                                    <div class="col-md-2 text-right">
                                        <span id="running_total" style="margin-right:20px;">0.00</span>
                                    </div>
                                </div>

                                <div class="col-md-12 mb-3">
                                    <button type="submit" name="menuAdd" class="btn btn-primary">Update Menu
                                        Item</button>
                                </div>
                                <div class="text-right"><a href="{{ route('admin.ingredient.add') }}" title="Add New Ingredient" target="_blank" class="btn btn-dark">New Ingredient</a>
                                </div>
                            </form>

                        </div>
                    </div>
                </div>
            <!-- /.container-fluid -->

        </div>
        <!-- End of Main Content -->

        <!-- Footer -->
        @include('layout.admin-footer')
        <!-- End of Footer -->

    </div>
    <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary" href="login.html">Logout</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap core JavaScript-->
    <script src="{{ asset('assets/js/jquery.min.js') }}"></script>
    <script src="{{ asset('assets/js/bootstrap.bundle.min.js') }}"></script>


    <!-- Core plugin JavaScript-->
    <script src="{{ asset('assets/js/jquery.easing.min.js') }}"></script>

    <!-- Custom scripts for all pages-->
    <script src="{{ asset('assets/js/sb-admin-2.min.js') }}"></script>



    <!--Datetime Picker JS and CSS-->
    <script src="{{ asset('assets/js/datetimepicker.js') }}"></script>

    <script>
        //vince added block
        $(document).ready(function() {
            $('input[required]').each(function() {
                // Find the label associated with the required input
                var label = $("label[for='" + $(this).attr('id') + "']");
                
                // Append a red asterisk to the label if it doesn't already have one
                if (label.length && !label.find('.required-asterisk').length) {
                    label.append('<span class="required-asterisk" style="color: red;font-size:1.2em;"> *</span>');
                }
            });
            //prevent form from submitting when enter is pressed on input elements
            $('input').on('keypress', function(event) {
                if (event.key === 'Enter') {
                    event.preventDefault();
                }
            });
        });
    //vince end
    </script>

    @php
        $ingredientOptions = [];
        foreach ($ingredient_list as $ingredient) {
            $ingredientOptions[] = [
                'id' => $ingredient->id,
                'name' => $ingredient->ingredient_name,
                'cost' => $ingredient->cost,
                'unit' => $ingredient->unit              
            ];
        }
    @endphp

    <script>
        let menuItemCount = {{ $menu->ingredients->count() }};
        let ingredientOptions = @json($ingredientOptions);

        function addMenuItem() {
            menuItemCount++;
            const container = document.getElementById('menu_items_container');
            const menuItemDiv = document.createElement('div');
            menuItemDiv.className = 'ingredient-item row mb-3';
            menuItemDiv.id = `menu_ingredient_${menuItemCount}`;

            const menuSelect = document.createElement('select');
            menuSelect.name = `menu_ingredients[${menuItemCount}][menu_id]`;
            menuSelect.className = 'form-control';
            menuSelect.required = true;

            ingredientOptions.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option.id;
                optionElement.textContent = option.name;
                optionElement.setAttribute('data-unit', option.unit); //vince added
                optionElement.setAttribute('data-cost', option.cost); //vince added
                menuSelect.appendChild(optionElement);
            });

            menuItemDiv.innerHTML = `
            <div class="col-md-4">
                <label>Item Name</label>
                ${menuSelect.outerHTML}
            </div>
            <div class="col-md-2">
                <label>Quantity</label>
                <input type="number" name="menu_ingredients[${menuItemCount}][quantity]" class="form-control item-quantity text-center" required min="0" step="any">
            </div>
            <div class="col-md-2">
                <label>Unit</label>
                <input type="text" name="menu_ingredients[${menuItemCount}][unit]" class="form-control item-unit" required readonly>
            </div>
            <div class="col-md-2">
                <label>Cost</label>
                <input type="text" name="menu_ingredients[${menuItemCount}][cost]" class="form-control item-cost text-right" required readonly>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="button" class="btn btn-danger" title="Remove" onclick="removeIngredient(${menuItemCount})"><i class="fa fa-trash"></i></button>
            </div>
        `;
            container.appendChild(menuItemDiv);

            //vince added block
            $newSelect = $(`select[name="menu_ingredients[${menuItemCount}][menu_id]"]`);
            $newSelect.on('change', function() {
                updateItem(this);
            });

            $newQtyInput = $(`input[name="menu_ingredients[${menuItemCount}][quantity]"]`);
            $newQtyInput.on('input', function() {
                updateCost(this);
            });
            //prevent form from submitting when enter is pressed on new input elements
            $('input').on('keypress', function(event) {
                if (event.key === 'Enter') {
                    event.preventDefault();
                }
            });
            $newSelect.trigger('change');
            $newQtyInput.trigger('input');
            //vince end
        }

        //vince added function
        function updateItem(selectElement){
            const selectedOption = selectElement.options[selectElement.selectedIndex];
            const selectedValue = selectedOption.value; // Get the selected option's value
            const unit = selectedOption.getAttribute('data-unit');
            const cost = parseFloat(selectedOption.getAttribute('data-cost'));
            const $inputUnit = $(selectElement).closest('.ingredient-item').find('input.item-unit');
            $inputUnit.val(unit);

            const $inputQty = $(selectElement).closest('.ingredient-item').find('input.item-quantity');
            const itemQty = parseFloat($inputQty.val()) || 0;
            const itemCost = itemQty*cost;
            
            const $inputCost = $(selectElement).closest('.ingredient-item').find('input.item-cost');
            $inputCost.val(itemCost.toFixed(2));

            updateTotalDisplay();

        }
        //vince end
        //vince added function
        function updateCost(inputElement) {
            // Access the select element as a jQuery object
            const $selectElement = $(inputElement).closest('.ingredient-item').find('select');
            if ($selectElement.length === 0) {
                console.error("No sibling select element found.");
                return;
            }

            const selectElement = $selectElement.get(0); // Get the plain DOM element

            // Access the selected option
            const selectedOption = selectElement.options[selectElement.selectedIndex];
            if (!selectedOption) {
                console.error("No option selected.");
                return;
            }

            const cost = parseFloat(selectedOption.getAttribute('data-cost')) || 0; // Get the 'data-cost' attribute
            const qty = parseFloat(inputElement.value) || 0; // Get the quantity from the input and convert to number

            // Find the input element for cost and update its value
            const $inputCost = $(inputElement).closest('.ingredient-item').find('input.item-cost');
            const totalCost = qty * cost;
            $inputCost.val(totalCost.toFixed(2)); // Set the calculated cost
            
            updateTotalDisplay();
        }
        //vince end
        function removeIngredient(itemId) {
            const item = document.getElementById(`menu_ingredient_${itemId}`);
            item.remove();
            updateTotalDisplay();
        }

        function calculateTotal() {
            let subtotal = 0;

            // Loop through each ingredient in jquery
            const $orderItems = $('.ingredient-item');
            $orderItems.each(function() {
                const itemTotal = parseFloat($(this).find('.item-cost').val()) || 0;
                subtotal += itemTotal;
            });
            /* const orderItems = document.querySelectorAll('.ingredient-item');
            orderItems.forEach(item => {
                const itemTotal = parseFloat(item.querySelector('.item-cost').value) || 0; // Convert to a number
                subtotal += itemTotal;
            }); */

            return subtotal.toFixed(2); // Return subtotal with 2 decimal places
        }

        // Function to update subtotal display
        function updateTotalDisplay() {
            const subtotalDisplay = document.getElementById('running_total');
            const subtotal = calculateTotal();

            if (subtotalDisplay) {
                subtotalDisplay.textContent = `${subtotal}`;
            }

        }
        
        // Initially update subtotal display
        updateTotalDisplay();
    </script>
    <script>
        $(document).ready(function() {
            //vince added block
            const $allSelects = $('.ingredient-item select');
            $allSelects.on('change', function() {
                updateItem(this);
            }); 

            const $newQtyInputs = $('.ingredient-item .item-quantity');
            $newQtyInputs.on('input', function() {
                updateCost(this);
            });
            //prevent form from submitting when enter is pressed on new input elements
            $('input').on('keypress', function(event) {
                if (event.key === 'Enter') {
                    event.preventDefault();
                }
            });
            $allSelects.trigger('change');
            $newQtyInputs.trigger('input');
            //vince end


        });
    </script>

</body>

</html>
