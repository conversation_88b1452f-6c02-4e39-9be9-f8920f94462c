@include('layout.admin-header')
{{-- @include('layout.scripts') --}}

<body id="page-top">


    <!-- Page Wrapper -->
    <div id="wrapper" data-bs-theme="light">

        <!-- Sidebar -->
        @include('layout.admin-sidebar')
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                @include('layout.admin-topbar')
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    @include('shared.success-message')
                    @include('shared.error-message')



                    <!-- Content Row -->
                    <div class="row">
                        <div class="container-fluid">
                            <h1 class="h3 mb-4 text-gray-800">Payment Settings</h1>

                            <!-- Information Alert -->
                            <div class="alert alert-info mb-4">
                                <h6 class="alert-heading"><i class="fas fa-info-circle"></i> Payment Gateway Configuration</h6>
                                <p class="mb-2">Configure your hotel's payment gateway credentials below. Each hotel can have its own PayPal and PayMongo settings.</p>
                                <ul class="mb-0">
                                    <li><strong>PayPal:</strong> Get your credentials from <a href="https://developer.paypal.com/" target="_blank">PayPal Developer Dashboard</a></li>
                                    <li><strong>PayMongo:</strong> Get your credentials from <a href="https://dashboard.paymongo.com/" target="_blank">PayMongo Dashboard</a></li>
                                    <li>Use <strong>sandbox/test</strong> credentials for testing, <strong>live/production</strong> for real transactions</li>
                                </ul>
                            </div>

                            <!-- Form for payment settings -->
                            <form action="{{ route('admin.settings.update') }}" method="POST">
                                @csrf

                                <!-- PayPal Settings Section -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="m-0 font-weight-bold text-primary">PayPal Configuration</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="form-group col-md-6">
                                                <label for="paypal_client_id">PayPal Client ID</label>
                                                <input autocomplete="off" type="text" name="paypal_client_id"
                                                    id="paypal_client_id" class="form-control"
                                                    value="{{ old('paypal_client_id', get_setting('paypal_client_id')) }}"
                                                    placeholder="Enter PayPal Client ID">
                                                <small class="form-text text-muted">Your PayPal application's Client ID</small>
                                            </div>
                                            <div class="form-group col-md-6">
                                                <label for="paypal_client_secret">PayPal Client Secret</label>
                                                <input autocomplete="off" type="password" name="paypal_client_secret"
                                                    id="paypal_client_secret" class="form-control"
                                                    value="{{ old('paypal_client_secret', get_setting('paypal_client_secret')) }}"
                                                    placeholder="Enter PayPal Client Secret">
                                                <small class="form-text text-muted">Your PayPal application's Client Secret</small>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-md-4">
                                                <label for="paypal_mode">PayPal Mode</label>
                                                <select name="paypal_mode" id="paypal_mode" class="form-control">
                                                    <option value="sandbox" {{ get_setting('paypal_mode', 'sandbox') == 'sandbox' ? 'selected' : '' }}>Sandbox (Testing)</option>
                                                    <option value="live" {{ get_setting('paypal_mode') == 'live' ? 'selected' : '' }}>Live (Production)</option>
                                                </select>
                                                <small class="form-text text-muted">Use sandbox for testing, live for production</small>
                                            </div>
                                            <div class="form-group col-md-4">
                                                <label for="paypal_currency">PayPal Currency</label>
                                                <input autocomplete="off" type="text" name="paypal_currency"
                                                    id="paypal_currency" class="form-control"
                                                    value="{{ old('paypal_currency', get_setting('paypal_currency', 'PHP')) }}"
                                                    placeholder="PHP">
                                                <small class="form-text text-muted">Currency code (e.g., PHP, USD)</small>
                                            </div>
                                            <div class="form-group col-md-4">
                                                <label for="paypal_service_fee">PayPal Service Fee (%)</label>
                                                <input autocomplete="off" type="number" step="0.01" name="paypal_service_fee"
                                                    id="paypal_service_fee" class="form-control"
                                                    value="{{ old('paypal_service_fee', get_setting('paypal_service_fee', '5')) }}"
                                                    placeholder="5.00">
                                                <small class="form-text text-muted">Service fee percentage</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- PayMongo Settings Section -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="m-0 font-weight-bold text-primary">PayMongo Configuration</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="form-group col-md-6">
                                                <label for="paymongo_public_key">PayMongo Public Key</label>
                                                <input autocomplete="off" type="text" name="paymongo_public_key"
                                                    id="paymongo_public_key" class="form-control"
                                                    value="{{ old('paymongo_public_key', get_setting('paymongo_public_key')) }}"
                                                    placeholder="pk_test_...">
                                                <small class="form-text text-muted">Your PayMongo Public Key</small>
                                            </div>
                                            <div class="form-group col-md-6">
                                                <label for="paymongo_secret_key">PayMongo Secret Key</label>
                                                <input autocomplete="off" type="password" name="paymongo_secret_key"
                                                    id="paymongo_secret_key" class="form-control"
                                                    value="{{ old('paymongo_secret_key', get_setting('paymongo_secret_key')) }}"
                                                    placeholder="sk_test_...">
                                                <small class="form-text text-muted">Your PayMongo Secret Key</small>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-md-6">
                                                <label for="paymongo_service_fee">PayMongo Service Fee (%)</label>
                                                <input autocomplete="off" type="number" step="0.01" name="paymongo_service_fee"
                                                    id="paymongo_service_fee" class="form-control"
                                                    value="{{ old('paymongo_service_fee', get_setting('paymongo_service_fee', '5')) }}"
                                                    placeholder="5.00">
                                                <small class="form-text text-muted">Service fee percentage</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-primary">Save Settings</button>
                            </form>

                        </div>
                    </div>


                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            @include('layout.admin-footer')
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary" href="login.html">Logout</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap core JavaScript-->
    <script src="{{ asset('assets/js/jquery.min.js') }}"></script>
    <script src="{{ asset('assets/js/bootstrap.bundle.min.js') }}"></script>

    <!-- Core plugin JavaScript-->
    <script src="{{ asset('assets/js/jquery.easing.min.js') }}"></script>

    <!-- Custom scripts for all pages-->
    <script src="{{ asset('assets/js/sb-admin-2.min.js') }}"></script>

    <!-- Page level plugins -->
    <script src="{{ asset('assets/js/Chart.min.js') }}"></script>

    <!-- Page level custom scripts -->
    <script src="{{ asset('assets/js/chart-area-demo.js') }}"></script>
    <script src="{{ asset('assets/js/chart-pie-demo.js') }}"></script>

</body>

</html>
