@include('layout.admin-header')

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        @include('layout.admin-sidebar')
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                @include('layout.admin-topbar')
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    @include('shared.success-message')
                    @include('shared.error-message')

                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Generate Invoice for Booking
                                #{{ $booking->id }}</h6>
                                <a href="{{route('admin.bookings')}}" class="btn btn-danger float-end">BACK</a>
                        </div>
                        <div class="card-body">
                            <!-- Invoice Form -->
                            <form action="{{ route('admin.invoice.store') }}" method="POST">
                                @csrf
                                <div class="row">

                                    <!-- Booking Details -->
                                    <div class="form-group col-md-6">
                                        <label for="booking_id">Booking ID</label>
                                        <input type="text" id="booking_id" name="booking_id" class="form-control"
                                            value="{{ $booking->id }}" readonly>
                                    </div>

                                    <div class="form-group col-md-6">
                                        <label for="guest_name">Guest Name</label>
                                        <input type="text" id="guest_name" name="guest_name" class="form-control"
                                            value="{{ $booking->guest_first_name }} {{ $booking->guest_last_name }}"
                                            readonly>
                                    </div>

                                    <div class="form-group col-md-6">
                                        <label for="room_details">Room Details</label>
                                        <input type="text" id="room_details" name="room_details" class="form-control"
                                            value="{{ $booking->room->room_number }} - {{ $booking->room->roomType->name }}"
                                            readonly>
                                    </div>

                                    <div class="form-group col-md-6">
                                        <label for="checkin_date">Check-in Date</label>
                                        <input type="text" id="checkin_date" name="checkin_date" class="form-control"
                                            value="{{ \Carbon\Carbon::parse($booking->checkin_date)->format('Y-m-d') }}"
                                            readonly>
                                    </div>

                                    <div class="form-group col-md-6">
                                        <label for="checkout_date">Check-out Date</label>
                                        <input type="text" id="checkout_date" name="checkout_date"
                                            class="form-control"
                                            value="{{ \Carbon\Carbon::parse($booking->checkout_date)->format('Y-m-d') }}"
                                            readonly>
                                    </div>

                                    <!-- Additional Charges -->
                                    {{-- <div class="form-group col-md-6">
                                        <label for="additional_charge_description">Description</label>
                                        <input type="text" id="additional_charge_description"
                                            name="additional_charge_description[]" class="form-control"
                                            placeholder="Description of charge">
                                    </div> --}}

                                    


                                    {{-- Additional Charges Container --}}
                                    <div class="form-group col-md-12" id="additional-charges-container">
                                        <!-- JavaScript will append additional charge fields here -->
                                    </div>

                                    <div class="form-group col-md-12">
                                        <button type="button" id="add-charge-btn" class="btn btn-secondary">Add
                                            Additional
                                            Charge</button>
                                    </div>

                                    {{-- Sub Total --}}
                                    <div class="form-group col-md-6">
                                        <label for="total_price">Sub Total</label>
                                        <input type="text" id="total_price" name="total_price"  class="form-control" value="{{$booking->total_price}}" readonly>
                                            
                                    </div>

                                    {{-- Hidden Input email created_by --}}
                                    <input type="hidden" name="created_by" value="{{get_user_email()}}">

                                    <!-- Total Price -->
                                    <div class="form-group col-md-6">
                                        <label for="total_amount">Total Amount</label>
                                        <input type="text" id="total_amount" name="total_amount" class="form-control" readonly>
                                    </div>

                                    
                                </div>

                                <input type="hidden" name="booking_id" value="{{ $booking->id }}">
                                <div class="form-group col-md-6">
                                    <button type="submit" class="btn btn-primary">Generate Invoice</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            @include('layout.admin-footer')
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary" href="login.html">Logout</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap core JavaScript-->
    <script src="{{ asset('assets/js/jquery.min.js') }}"></script>
    <script src="{{ asset('assets/js/bootstrap.bundle.min.js') }}"></script>

    <!-- Core plugin JavaScript-->
    <script src="{{ asset('assets/js/jquery.easing.min.js') }}"></script>

    <!-- Custom scripts for all pages-->
    <script src="{{ asset('assets/js/sb-admin-2.min.js') }}"></script>

    <!-- Additional Charges JS -->
    <script>
        $(document).ready(function() {
            let roomSubTotal = {{ $booking->total_price }};

            let baseChargeHtml = `
        <div class="form-group row additional-charge-row">
            <div class="col-md-5">
                <label for="additional_charge_description">Description</label>
                <input type="text" name="additional_charge_description[]" class="form-control" placeholder="Description of charge">
            </div>
            <div class="col-md-5">
                <label for="additional_charge_amount">Amount</label>
                <input type="number" name="additional_charge_amount[]" class="form-control" placeholder="Amount">
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="button" class="btn btn-danger remove-charge-btn">Remove</button>
            </div>
        </div>
    `;

            $('#add-charge-btn').click(function() {
                $('#additional-charges-container').append(baseChargeHtml);
            });

            $(document).on('click', '.remove-charge-btn', function() {
                $(this).closest('.additional-charge-row').remove();
                calculateTotalAmount();
            });

            function calculateTotalAmount() {
                let total = roomSubTotal; // Start with the room subtotal
                $('input[name="additional_charge_amount[]"]').each(function() {
                    let amount = parseFloat($(this).val()) || 0;
                    total += amount;
                });
                $('#total_amount').val(total.toFixed(2));
            }

            $(document).on('input', 'input[name="additional_charge_amount[]"]', function() {
                calculateTotalAmount();
            });

            // Initial calculation
            calculateTotalAmount();
        });
    </script>
</body>

</html>
