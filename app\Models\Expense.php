<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\BelongsToHotel;

class Expense extends Model
{
    use HasFactory, BelongsToHotel;

    protected $fillable = [
        'hotel_id',
        'description',
        'amount',
        'expense_type',
        'expense_date',
        'invoice_id',
        'grocery_item_id',
        'created_by',
    ];

    public function invoice()
    {
        return $this->belongsTo(Invoice::class, 'invoice_id');
    }

    public function groceryItem()
    {
        return $this->belongsTo(Grocery_Item::class, 'grocery_item_id');
    }
}
