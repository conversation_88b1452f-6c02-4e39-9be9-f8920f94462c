<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Models\Hotel;
use App\Models\Setting;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add payment gateway settings to existing hotels
        $hotels = Hotel::all();
        
        foreach ($hotels as $hotel) {
            $paymentSettings = [
                ['hotel_id' => $hotel->id, 'key' => 'paypal_client_id', 'value' => ''],
                ['hotel_id' => $hotel->id, 'key' => 'paypal_client_secret', 'value' => ''],
                ['hotel_id' => $hotel->id, 'key' => 'paypal_mode', 'value' => 'sandbox'],
                ['hotel_id' => $hotel->id, 'key' => 'paypal_currency', 'value' => 'PHP'],
                ['hotel_id' => $hotel->id, 'key' => 'paymongo_public_key', 'value' => ''],
                ['hotel_id' => $hotel->id, 'key' => 'paymongo_secret_key', 'value' => ''],
            ];

            foreach ($paymentSettings as $setting) {
                Setting::updateOrCreate(
                    ['hotel_id' => $setting['hotel_id'], 'key' => $setting['key']],
                    ['value' => $setting['value']]
                );
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove payment gateway settings
        $paymentKeys = [
            'paypal_client_id',
            'paypal_client_secret', 
            'paypal_mode',
            'paypal_currency',
            'paymongo_public_key',
            'paymongo_secret_key'
        ];

        Setting::whereIn('key', $paymentKeys)->delete();
    }
};
