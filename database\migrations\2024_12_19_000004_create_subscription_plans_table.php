<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscription_plans', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->decimal('price', 10, 2);
            $table->enum('billing_cycle', ['monthly', 'yearly']);
            $table->json('features'); // Store plan features as JSON
            $table->integer('max_rooms')->default(0); // 0 = unlimited
            $table->integer('max_users')->default(0); // 0 = unlimited
            $table->boolean('is_active')->default(true);
            $table->boolean('is_popular')->default(false);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });

        // Create default subscription plans
        DB::table('subscription_plans')->insert([
            [
                'name' => 'Starter',
                'slug' => 'starter',
                'description' => 'Perfect for small hotels and B&Bs',
                'price' => 29.99,
                'billing_cycle' => 'monthly',
                'features' => json_encode([
                    'Up to 20 rooms',
                    'Up to 5 users',
                    'Basic reporting',
                    'Email support',
                    'Online booking',
                    'Payment processing'
                ]),
                'max_rooms' => 20,
                'max_users' => 5,
                'is_active' => true,
                'is_popular' => false,
                'sort_order' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ]
        ]);

        DB::table('subscription_plans')->insert([
            [
                'name' => 'Professional',
                'slug' => 'professional',
                'description' => 'Ideal for medium-sized hotels',
                'price' => 59.99,
                'billing_cycle' => 'monthly',
                'features' => json_encode([
                    'Up to 100 rooms',
                    'Up to 20 users',
                    'Advanced reporting',
                    'Priority support',
                    'Online booking',
                    'Payment processing',
                    'Restaurant management',
                    'Event management',
                    'Custom branding'
                ]),
                'max_rooms' => 100,
                'max_users' => 20,
                'is_active' => true,
                'is_popular' => true,
                'sort_order' => 2,
                'created_at' => now(),
                'updated_at' => now(),
            ]
        ]);

        DB::table('subscription_plans')->insert([
            [
                'name' => 'Enterprise',
                'slug' => 'enterprise',
                'description' => 'For large hotels and chains',
                'price' => 149.99,
                'billing_cycle' => 'monthly',
                'features' => json_encode([
                    'Unlimited rooms',
                    'Unlimited users',
                    'Advanced analytics',
                    '24/7 phone support',
                    'Online booking',
                    'Payment processing',
                    'Restaurant management',
                    'Event management',
                    'Custom branding',
                    'API access',
                    'Multi-location support',
                    'Custom integrations'
                ]),
                'max_rooms' => 0,
                'max_users' => 0,
                'is_active' => true,
                'is_popular' => false,
                'sort_order' => 3,
                'created_at' => now(),
                'updated_at' => now(),
            ]
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_plans');
    }
};
