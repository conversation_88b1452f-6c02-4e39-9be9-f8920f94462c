<?php

namespace App\Http\Controllers;

use App\Models\Hotel;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;

class HotelRegistrationController extends Controller
{
    /**
     * Show the hotel registration form.
     */
    public function showRegistrationForm()
    {
        return view('hotel-registration');
    }

    /**
     * Handle hotel registration.
     */
    public function register(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:hotels,email',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'admin_name' => 'required|string|max:255',
            'admin_email' => 'required|email|unique:users,email',
            'admin_password' => 'required|string|min:8|confirmed',
        ]);

        try {
            // Create hotel
            $hotel = Hotel::create([
                'name' => $validatedData['name'],
                'slug' => Str::slug($validatedData['name']),
                'email' => $validatedData['email'],
                'phone' => $validatedData['phone'],
                'address' => $validatedData['address'],
                'description' => $validatedData['name'] . ' - Hotel Management',
                'status' => 'active',
                'trial_ends_at' => now()->addDays(30), // 30-day trial
            ]);

            // Create admin user for the hotel
            $adminUser = User::create([
                'name' => $validatedData['admin_name'],
                'email' => $validatedData['admin_email'],
                'password' => Hash::make($validatedData['admin_password']),
                'user_type' => 'admin',
                'role' => 'admin',
            ]);

            // Attach user to hotel
            $hotel->users()->attach($adminUser->id, [
                'role' => 'admin',
                'is_active' => true,
            ]);

            // Create default room types for the hotel
            $defaultRoomTypes = [
                ['name' => 'Single', 'description' => 'Single Room', 'rate' => 1000.00],
                ['name' => 'Double', 'description' => 'Double Room', 'rate' => 1500.00],
                ['name' => 'Family', 'description' => 'Family Room', 'rate' => 2000.00],
                ['name' => 'Suite', 'description' => 'Suite Room', 'rate' => 2500.00],
            ];

            foreach ($defaultRoomTypes as $roomType) {
                $hotel->roomTypes()->create($roomType);
            }

            // Create default settings for the hotel
            $defaultSettings = [
                ['key' => 'site_name', 'value' => $hotel->name],
                ['key' => 'site_description', 'value' => $hotel->description],
                ['key' => 'contact_email', 'value' => $hotel->email],
                ['key' => 'phone', 'value' => $hotel->phone ?? ''],
                ['key' => 'address', 'value' => $hotel->address ?? ''],
                ['key' => 'tin', 'value' => ''],
                ['key' => 'paypal_service_fee', 'value' => '5'],
                ['key' => 'paymongo_service_fee', 'value' => '5'],
                // PayPal Configuration
                ['key' => 'paypal_client_id', 'value' => ''],
                ['key' => 'paypal_client_secret', 'value' => ''],
                ['key' => 'paypal_mode', 'value' => 'sandbox'],
                ['key' => 'paypal_currency', 'value' => 'PHP'],
                // PayMongo Configuration
                ['key' => 'paymongo_public_key', 'value' => ''],
                ['key' => 'paymongo_secret_key', 'value' => ''],
            ];

            foreach ($defaultSettings as $setting) {
                $hotel->settings()->create($setting);
            }

            // Log the admin user in
            Auth::login($adminUser);
            
            // Set the current hotel in session
            session(['current_hotel_id' => $hotel->id]);

            // Log activity
            log_activity('Hotel registered: ' . $hotel->name, 'Hotel Registration');

            return redirect()->route('admin.index')->with('success', 
                'Welcome to your hotel management system! Your 30-day free trial has started.');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Registration failed. Please try again.'])->withInput();
        }
    }
}
