@include('layout.admin-header')
{{-- @include('layout.scripts') --}}

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        @include('layout.admin-sidebar')
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                @include('layout.admin-topbar')
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    @include('shared.success-message')
                    @include('shared.error-message')
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Add Event</h6>
                            <span class="small"><mark>Enter check-in and check-out dates first to see available
                                rooms</mark></span>
                            <!--vince<a href="{{ route('admin.events') }}" class="btn btn-danger float-end">BACK</a>-->
                        </div>
                        <div class="card-body">


                            <!-- Include Select2 CSS -->
                            <link href="{{ asset('assets/css/select2.min.css') }}" rel="stylesheet" />
                            <!-- Include Select2 JS -->
                            <script src="{{ asset('assets/js/select2.min.js') }}" defer></script>
                            {{-- Form to Create Event --}}
                            <form action="{{ route('admin.event.store') }}" method="POST">
                                @csrf
                                <div class="row">
                                    
                                    <div class="form-group col-md-6">
                                        <label for="guest_info">Select Guest</label>
                                        <select class="form-control select2-guest" id="guest_info" name="guest_info"
                                            style="width: 100%;">
                                            <option value="">-- Select Guest --</option>
                                            <!-- Options will be dynamically added here -->
                                        </select>
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="checkin_date">Event Start</label>
                                        <input type="text" class="form-control" id="checkin_date" name="start_date"
                                            required autocomplete="off">
                                    </div>

                                    <div class="form-group col-md-3">
                                        <label for="checkout_date">Event End <span id="no_of_days" class="bg-warning small" style="border-radius:3px;padding:3px;">1 day</span></span></label>
                                        <input type="text" id="checkout_date" class="form-control" name="end_date" required autocomplete="off">
                                    </div>

                                    <div class="form-group col-md-3">
                                        <label for="customer_first_name">First Name</label>
                                        <input class="form-control" id="customer_first_name" name="customer_first_name"
                                        autocomplete="off" placeholder="*" required>
                                    </div>

                                    <div class="form-group col-md-3">
                                        <label for="customer_last_name">Last Name</label>
                                        <input class="form-control" id="customer_last_name" name="customer_last_name"
                                        autocomplete="off" placeholder="*" required>
                                    </div>
                                    <div class="form-group col-md-3">
                                        <!--vince<div class="btn-group">-->
                                            <label for="filter-dropdown">Room Type</label><?php //vince added label
                                        ?>
                                            <button type="button" class="btn btn-primary dropdown-toggle" style="min-width:100%;"
                                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" id="filter-dropdown">
                                                Filter by Room Type
                                            </button>
                                            <div class="dropdown-menu" style="min-width:100%;">
                                                <a class="dropdown-item filter-btn active" data-filter="All"
                                                    href="#">All</a>
                                                @foreach ($room_types as $room_Type)
                                                    <a class="dropdown-item filter-btn"
                                                        data-filter="{{ $room_Type->name }}"
                                                        href="#">{{ ucfirst($room_Type->name) }}</a>
                                                @endforeach
                                            </div>
                                        <!--vince</div>-->
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="room_id">Room<span class="required-asterisk" style="color: red;font-size:1.2em;"> *</span></label>

                                        <select class="form-control select2-room" id="room_id" name="room_id" style="width: 100%;"
                                            required>
                                            <option value="">Select Room</option>
                                        </select>
                                    </div>

                                    <div class="form-group col-md-3">
                                        <label for="customer_phone">Phone</label>
                                        <input class="form-control" id="customer_phone" name="customer_phone"
                                            autocomplete="off" placeholder="*" required>
                                    </div>

                                    <div class="form-group col-md-3">
                                        <label for="customer_email">Email</label>
                                        <input class="form-control" id="customer_email" name="customer_email"
                                            autocomplete="off" placeholder="---">
                                    </div>

                                    <!-- Order Items Subtotal -->
                                    <div class="col-md-3">
                                        <label>Catering Sub-Total</label>
                                        <input type="text" id="order_items_total" class="form-control text-right" readonly>
                                    </div>
                                    <!-- Room Total -->
                                    <div class="col-md-3">
                                        <label>Room Rate</label>
                                        <input type="text" id="room_total" class="form-control text-right" readonly>
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label for="name">Event Name</label>
                                        <input class="form-control" id="name" name="name" autocomplete="off"
                                            placeholder="*" required>
                                    </div>                                    
                                    <div class="form-group col-md-3">
                                        <label for="status">Event Status<span class="required-asterisk" style="color: red;font-size:1.2em;"> *</span></label>
                                        <select class="form-control" id="status" name="status">
                                            <option value="booked">Booked</option>
                                            <option value="pending">Pending</option>
                                            <option value="cancelled">Cancelled</option>
                                        </select>
                                    </div>
                                   
                                    <!-- Final Total -->
                                    <div class="col-md-3">
                                        <label for="final_total_price_visible">Event Sub-Total</label>
                                        <input type="text" id="final_total_price_visible" name="sub_total_visible"
                                            class="form-control text-right" style="font-size:1.2em;" readonly>
                                        <input type="hidden" id="final_total_price" name="sub_total">
                                    </div>
                                    <div class="form-group col-md-12">
                                        <label for="description">Event Particulars</label>
                                        <textarea class="form-control" name="description" autocomplete="off"
                                            placeholder="---"></textarea>
                                    </div>
                                    <div class="form-group col-md-12">
                                        <button type="button" class="btn btn-success" onclick="addOrderItem()">Add Catering Item</button>
                                    </div>
                                    <div class="col-md-12 mb-3">
                                        
                                        <div id="order_items_container">
                                            <!-- Order items will be appended here -->
                                        </div>

                                    </div>
                                    

                                    
                                    {{-- Hidden Inputs to track who created this event --}}
                                    <input type="hidden" name="created_by"
                                        value="@if (Auth::check() && Auth::user()->email) {{ Auth::user()->email }}@else no_user @endif">
                                    
                                </div>
                                <div class="row">
                                    <div class="col-md-6"></div>
                                    <div class="col-md-2 text-center">
                                        Total Catering:
                                    </div>
                                    <div class="col-md-2 text-right">
                                        <span id="running_total" style="margin-right:12px;">0.00</span>
                                    </div>
                                </div>
                                <div class="form-group col-md-12 text-center">
                                    <button type="submit" class="btn btn-primary mt-5">Create Event</button>
                                </div>
                            </form>




                        </div>
                    </div>
                </div>
            </div>


            <!-- End of Main Content -->
            <!-- Footer -->
            @include('layout.admin-footer')
            <!-- End of Footer -->


        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary" href="login.html">Logout</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap core JavaScript-->
    <script src="{{ asset('assets/js/jquery.min.js') }}"></script>
    <script src="{{ asset('assets/js/bootstrap.bundle.min.js') }}"></script>

    <!-- Core plugin JavaScript-->
    <script src="{{ asset('assets/js/jquery.easing.min.js') }}"></script>

    <!-- Custom scripts for all pages-->
    <script src="{{ asset('assets/js/sb-admin-2.min.js') }}"></script>

    <!-- Datetime Picker JS and CSS-->
    <script src="{{ asset('assets/js/datetimepicker.js') }}"></script>
    <script>
    //vince added block
        $(document).ready(function() {
            $('input[required]').each(function() {
                // Find the label associated with the required input
                var label = $("label[for='" + $(this).attr('id') + "']");
                
                // Append a red asterisk to the label if it doesn't already have one
                if (label.length && !label.find('.required-asterisk').length) {
                    label.append('<span class="required-asterisk" style="color: red;font-size:1.2em;"> *</span>');
                }
            });
        });
    //vince end
    </script>
    <!-- JavaScript Initialize for Date Picker -->
    <script>
        $(document).ready(function() {
            var tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1); // Add 1 day for tomorrow
            tomorrow.setHours(12, 0, 0, 0); // Set time to 12:00 PM (noon)

            var dayaftertomorrow = new Date();
            dayaftertomorrow.setDate(dayaftertomorrow.getDate() + 2); // Add 2 days
            dayaftertomorrow.setHours(12, 0, 0, 0); // Set time to 12:00 PM (noon)


            $('#checkin_date').datetimepicker({
                format: 'Y-m-d H:i', // Full date and time format
                step: 30, // 30 minutes
                value: tomorrow,
                autoclose: true // Automatically close after selecting the date and time
            });

            $('#checkout_date').datetimepicker({
                format: 'Y-m-d H:i', // Full date and time format
                step: 30, // 30 minutes
                value: dayaftertomorrow,
                autoclose: true // Automatically close after selecting the date and time
            });
        });
    </script>
    @php
        // Initialize an empty array to store menu items
        $menuOptions = [];
        // Populate the array with menu items
        foreach ($menu_list as $menu) {
            $menuOptions[] = ['id' => $menu->id, 'name' => $menu->name, 'price' => $menu->price];
        }
    @endphp

    <script>
        let orderItemCount = 0;
        let menuOptions = @json($menuOptions);


        // Function to add order item dynamically
        function addOrderItem() {
            orderItemCount++;
            const container = document.getElementById('order_items_container');
            const orderItemDiv = document.createElement('div');
            orderItemDiv.className = 'order-item row mb-3';
            orderItemDiv.id = `order_item_${orderItemCount}`;

            // Create a dropdown menu for item name
            const menuSelect = document.createElement('select');
            menuSelect.name = `order_items[${orderItemCount}][menu_id]`;
            menuSelect.className = 'form-control menu-select select3';
            menuSelect.dataset.orderItemIndex = orderItemCount; // Store the index for reference
            menuSelect.required = true;

            // Populate the dropdown with menu items
            menuOptions.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option.id;
                optionElement.textContent = option.name;
                optionElement.setAttribute('data-price', option.price); //vince added
                menuSelect.appendChild(optionElement);
            });

            orderItemDiv.innerHTML = `
        <div class="col-md-4">
            <label>Item Name</label>
            ${menuSelect.outerHTML}
        </div>
        <div class="col-md-2">
            <label>Quantity</label>
            <input type="text" name="order_items[${orderItemCount}][quantity]" class="form-control quantity-input text-center" data-order-item-index="${orderItemCount}" value="1" min="1" autocomplete="off" required>
        </div>
        <div class="col-md-2">
            <label>Price</label>
            <input type="text" name="order_items[${orderItemCount}][price]" class="form-control price-input text-right" data-order-item-index="${orderItemCount}" readonly>
        </div>
        <div class="col-md-2">
            <label>Item Total</label>
            <input type="text" name="order_items[${orderItemCount}][itemtotal]" class="form-control itemtotal-input text-right" data-order-item-index="${orderItemCount}" readonly>
        </div>
        <div class="col-md-2 d-flex align-items-end">
            <button type="button" class="btn btn-danger" onclick="removeOrderItem(${orderItemCount})" title="Remove"><i class="fa fa-trash"></i></button>
        </div>
    `;

            container.appendChild(orderItemDiv);
            // Initialize Select2 for the newly added dropdown
            $(`#order_item_${orderItemCount} .select3`).select2({
                placeholder: 'Select a Food Item',
                allowClear: true
            });

            // Add event listener for item selection to update the price
            $newSelect = $(`select[name="order_items[${orderItemCount}][menu_id]"]`)
            $newSelect.on('select2:select', function() {
                updatePrice(this);
            });

            // Add event listener to quantity input to recalculate subtotal when changed
            $newQtyInput = $(`input[name="order_items[${orderItemCount}][quantity]"]`)
            $newQtyInput.on('input', function() {
                updateCost(this);
            });

            //prevent form submission when enter is pressed on item qty
            $('.quantity-input').on('keypress', function(event) {
                if (event.key === 'Enter') {
                    event.preventDefault();
                }
            });
            // Update price initially for the added item
            $newSelect.val(0).trigger('change');
            $newQtyInput.trigger('input');
            
        }


        // Function to update the price when the Item is changed
        function updatePrice(selectElement) {

            const selectedOption = selectElement.options[selectElement.selectedIndex];
            const selectedValue = selectedOption.value; // Get the selected option's value
            const price = parseFloat(selectedOption.getAttribute('data-price'));
            const $inputPrice = $(selectElement).closest('.order-item').find('input.price-input');
            $inputPrice.val(price.toFixed(2));

            const $inputQty = $(selectElement).closest('.order-item').find('input.quantity-input');
            const qty = $inputQty.val();
            const $inputCost = $(selectElement).closest('.order-item').find('input.itemtotal-input');
            const itemCost = qty*price;
            $inputCost.val(itemCost.toFixed(2));
            
            updateSubtotal();
        }

        function updateCost(inputElement) {
            // Access the select element as a jQuery object
            const $selectElement = $(inputElement).closest('.order-item').find('select');
            if ($selectElement.length === 0) {
                console.error("No sibling select element found.");
                return;
            }

            const selectElement = $selectElement.get(0); // Get the plain DOM element

            // Access the selected option
            const selectedOption = selectElement.options[selectElement.selectedIndex];
            if (!selectedOption) {
                console.error("No option selected.");
                return;
            }

            const price = parseFloat(selectedOption.getAttribute('data-price')) || 0; // Get the 'data-cost' attribute
            const qty = parseFloat(inputElement.value) || 0; // Get the quantity from the input and convert to number

            // Find the input element for cost and update its value
            const $inputCost = $(inputElement).closest('.order-item').find('input.itemtotal-input');
            const totalCost = qty * price;
            $inputCost.val(totalCost.toFixed(2)); // Set the calculated cost
            
            updateSubtotal();
        }

        // Function to remove order item
        function removeOrderItem(index) {
            const item = document.getElementById(`order_item_${index}`);
            if (item) {
                item.remove();
                updateSubtotal();
            }
        }
        // Function to calculate the final total price
        function updateFinalTotal() {
            const orderItemsTotal = parseFloat(document.getElementById('order_items_total').value) || 0;
            const roomTotal = parseFloat(document.getElementById('room_total').value) || 0;

            const finalTotal = orderItemsTotal + roomTotal;

            // Update the final total price input
            document.getElementById('final_total_price_visible').value = finalTotal.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });//vince orig .toFixed(2)
            $('#final_total_price').val(finalTotal.toFixed(2));//vince added
        }


        // Function to update the subtotal display for order items
        function updateSubtotal() {
            let orderItemsTotal = 0;

            // Iterate through each order item to calculate the total
            document.querySelectorAll('.order-item').forEach(orderItem => {
                const priceInput = orderItem.querySelector('.price-input');
                const quantityInput = orderItem.querySelector('.quantity-input');

                if (priceInput && quantityInput) {
                    const price = parseFloat(priceInput.value) || 0;
                    const quantity = parseFloat(quantityInput.value) || 0;//vince orig parseInt(quantityInput.value) || 0;
                    orderItemsTotal += price * quantity;
                }
            });

            // Update the order items total input
            document.getElementById('order_items_total').value = orderItemsTotal.toFixed(2);
            document.getElementById('running_total').textContent = orderItemsTotal.toFixed(2);
            // Recalculate the final total price
            updateFinalTotal();
        }


        // Initially update subtotal display
        updateSubtotal();
    </script>

    {{-- Script that dynamically gets available room options based on checkin_date and checkout_date --}}
    <script>
        $(document).ready(function() {
            var roomData = []; // Store room data here

            // Function to fetch available rooms
            function fetchAvailableRooms(checkinDate, checkoutDate) {
                $.ajax({
                    url: '{{ route('get.available.rooms') }}',
                    method: 'POST',
                    data: {
                        checkin_date: checkinDate,
                        checkout_date: checkoutDate,
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        roomData = response; // Store the fetched room data
                        updateRoomOptions(); // Update the room options
                    }
                });
            }

            // Event listener for date change
            $('#checkin_date, #checkout_date').on('change', function() {
                var checkinDate = $('#checkin_date').val();
                var checkoutDate = $('#checkout_date').val();
                if (checkinDate && checkoutDate) {
                    fetchAvailableRooms(checkinDate, checkoutDate);
                }
            });

            function updateRoomOptions() {
                var selectedFilter = $('.dropdown-item.active').data('filter');
                $('#room_id').empty();
                $('#filter-dropdown').text(selectedFilter);//vince added
                var filteredRooms = roomData.filter(function(room) {
                    return selectedFilter === 'All' || (room.room_type && room.room_type.name ===
                        selectedFilter);
                });

                if (filteredRooms.length > 0) {
                    $.each(filteredRooms, function(index, room) {
                        const roomTypeName = room.room_type ? room.room_type.name : 'Unknown Room Type';
                        const roomNumber = room.room_number ? room.room_number : 'Undefined';
                        const roomRate = room.room_type ? room.individual_room_rate : 0;

                        $('#room_id').append('<option value="' + room.id + '" data-price="' + roomRate +
                            '">' + roomNumber + ' - ' + roomTypeName + ' ('+ roomRate +')</option>');
                    });
                } else {
                    $('#room_id').append('<option value="">No rooms available</option>');
                }

                calculateTotalPrice(); // Ensure total price is recalculated after options are updated
            }

            // Function to calculate the room total price
            function calculateTotalPrice() {
                let roomTotal = 0;
                const selectedRoom = $('#room_id').find(':selected');

                if (selectedRoom.length > 0) {
                    const roomRate = parseFloat(selectedRoom.data('price')) || 0;
                    const checkInDate = new Date($('#checkin_date').val());
                    const checkOutDate = new Date($('#checkout_date').val());
                    const timeDiff = checkOutDate - checkInDate;
                    const daysDiff = Math.ceil(timeDiff / (1000 * 60 * 60 * 24)); // Round up to the nearest day

                    roomTotal = roomRate * daysDiff;
                    //vince added block
                    var days = ' day';
                    if (daysDiff > 1){
                        days = " days";
                    }
                    $('#no_of_days').text(daysDiff + days);
                    //vince end
                    // Update the room total input
                    $('#room_total').val(roomTotal.toFixed(2));
                } else {
                    $('#room_total').val(''); // Clear the room total field if no room is selected
                }

                // Recalculate the final total price
                updateFinalTotal();
            }

            // Handle dropdown item click
            $(document).on('click', '.dropdown-item', function() {
                $('.dropdown-item').removeClass('active');
                $(this).addClass('active');
                updateRoomOptions(); // Update room options based on selected filter
            });

            // Event listener for room selection change
            $('#room_id').on('change', function() {
                calculateTotalPrice();
            });

            // Event listener for date change
            $('#checkin_date, #checkout_date').on('change', function() {
                calculateTotalPrice();
            });

            //vince added block
            var defaultcheckinDate = $('#checkin_date').val();
            var defaultcheckoutDate = $('#checkout_date').val();
            fetchAvailableRooms(defaultcheckinDate, defaultcheckoutDate);
            //vince end

            // Initial call to set default filter and calculate total price
            updateRoomOptions();
        });
    </script>
    <!-- Initialize Select2 and Handle Guest Information -->
    <script>
        $(document).ready(function() {
            //prevent form from submitting when enter is pressed on input elements
            $('input').on('keypress', function(event) {
                if (event.key === 'Enter') {
                    event.preventDefault();
                }
            });
            // Initialize Select2 for room selection
            $('.select2-room').select2({
                placeholder: 'Select a Room',
                allowClear: true
            }); 
            // Room selection change event to recalculate price
            /* vince $('.select2-room').on('change', function() {
                calculateTotalPrice(); // Reuse your existing function
            }); */
            //vince added block
            $('#room_id').on('select2:select', function(e) {
                calculateTotalPrice();
            });
            //vince end
            $('.select2-guest').select2({
                //vince tags: true, // Allows users to add new items
                placeholder: 'Select Guest',
                allowClear: true,
                ajax: {
                    url: "{{ route('admin.ajax.getGuests') }}", // Your API endpoint
                    dataType: 'json',
                    delay: 250,
                    data: function(params) {
                        return {
                            q: params.term // Query parameter for search
                        };
                    },
                    processResults: function(data) {
                        // Transform the API response into the format required by Select2
                        return {
                            results: data.map(function(guest) {
                                return {
                                    id: guest.id,
                                    text: guest.first_name + ' ' + guest.last_name,
                                    first_name: guest.first_name,
                                    last_name: guest.last_name,
                                    email: guest.email,
                                    phone: guest.phone
                                };
                            })
                        };
                    },
                    cache: true
                }
            });
            
            $('#guest_info').on('select2:select', function(e) {
                var data = e.params.data;

                // Assuming data contains guest details like this
                $('#customer_first_name').val(data.first_name || '');
                $('#customer_last_name').val(data.last_name || '');
                $('#customer_email').val(data.email || '');
                $('#customer_email').attr('title', data.email || '');//vince added
                $('#customer_phone').val(data.phone || '');
            });
        });
    </script>


</body>

</html>
