@include('layout.admin-header')
@php
    $currencySymbol = config('currency.symbol');
@endphp

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        @include('layout.admin-sidebar')
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                @include('layout.admin-topbar')
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    @include('shared.success-message')
                    @include('shared.error-message')
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Bank Remittances Single</h6>
                            <div class="row">

                            </div>
                        </div>
                        <div class="card-body">
                            <h1>Bank Remittance Upload Single</h1>

                            
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Remittance ID</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                       
                                            <tr>
                                                <td>{{ $remittance->id }}</td>
                                                <td>{{ $remittance->amount }}</td>
                                                <td>{{ $remittance->status }}</td>
                                            </tr>
                                      
                                    </tbody>
                                </table>

                            <!-- Optionally, handle payments here if needed -->
                                <h3>Related Payments</h3>
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Payment ID</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($payments as $payment)
                                            <tr>
                                                <td>{{ $payment->id }}</td>
                                                <td>{{ $payment->amount }}</td>
                                                <td>{{ $payment->status }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                        </div>

                         {{-- upload documents --}}
                         <div class="card-body">
                            <h1>Upload Receipt</h1>
                            <form action="{{ route('remittance.documents.upload.single') }}" method="POST" enctype="multipart/form-data">
                                @csrf
                                    <input type="hidden" name="remittance_id" value="{{ $remittance->id }}">
                                
                                <div class="form-group {{ $errors->has('documents.*') ? 'has-error' : '' }}">
                                    <label for="documents">Documents</label>
                                    <input type="file" class="form-control" name="documents[]" id="documents"
                                        multiple>
                                    @if ($errors->has('documents.*'))
                                        <span class="text-danger">{{ $errors->first('documents.*') }}</span>
                                    @endif
                                </div>
                                <button type="submit" class="btn btn-primary">Upload</button>
                            </form>
                        </div>
                    </div>



                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            @include('layout.admin-footer')
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary" href="login.html">Logout</a>
                </div>
            </div>
        </div>
    </div>

    @include('layout.scripts')

</body>

</html>
