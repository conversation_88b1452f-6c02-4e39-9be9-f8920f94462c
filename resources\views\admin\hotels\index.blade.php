@include('layout.admin-header')

<body id="page-top">
    <!-- Page Wrapper -->
    <div id="wrapper" data-bs-theme="light">
        <!-- Sidebar -->
        @include('layout.admin-sidebar')
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">
            <!-- Main Content -->
            <div id="content">
                <!-- Topbar -->
                @include('layout.admin-topbar')
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    @include('shared.success-message')
                    @include('shared.error-message')

                    <!-- Page Heading -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">Hotel Management</h1>
                        <a href="{{ route('admin.hotels.create') }}" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
                            <i class="fas fa-plus fa-sm text-white-50"></i> Add New Hotel
                        </a>
                    </div>

                    <!-- Hotels Table -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">All Hotels</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Name</th>
                                            <th>Email</th>
                                            <th>Phone</th>
                                            <th>Status</th>
                                            <th>Users</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($hotels as $hotel)
                                        <tr>
                                            <td>{{ $hotel->id }}</td>
                                            <td>
                                                <strong>{{ $hotel->name }}</strong>
                                                <br>
                                                <small class="text-muted">{{ $hotel->slug }}</small>
                                            </td>
                                            <td>{{ $hotel->email }}</td>
                                            <td>{{ $hotel->phone ?? 'N/A' }}</td>
                                            <td>
                                                <span class="badge badge-{{ $hotel->status === 'active' ? 'success' : ($hotel->status === 'suspended' ? 'danger' : 'warning') }}">
                                                    {{ ucfirst($hotel->status) }}
                                                </span>
                                            </td>
                                            <td>{{ $hotel->users->count() }}</td>
                                            <td>{{ $hotel->created_at->format('M d, Y') }}</td>
                                            <td>
                                                <a href="{{ route('admin.hotels.show', $hotel) }}" class="btn btn-info btn-sm">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.hotels.edit', $hotel) }}" class="btn btn-warning btn-sm">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form action="{{ route('admin.switch-hotel') }}" method="POST" style="display: inline;">
                                                    @csrf
                                                    <input type="hidden" name="hotel_id" value="{{ $hotel->id }}">
                                                    <button type="submit" class="btn btn-primary btn-sm" title="Switch to this hotel">
                                                        <i class="fas fa-exchange-alt"></i>
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- Pagination -->
                            <div class="d-flex justify-content-center">
                                {{ $hotels->links() }}
                            </div>
                        </div>
                    </div>
                </div>
                <!-- /.container-fluid -->
            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            @include('layout.admin-footer')
            <!-- End of Footer -->
        </div>
        <!-- End of Content Wrapper -->
    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>
</body>
