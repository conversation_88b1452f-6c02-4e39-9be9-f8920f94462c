<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('invoice_id')->nullable(); // Foreign key to invoices table
            $table->decimal('amount', 10, 2); // Payment amount
            $table->string('payment_method')->nullable(); // e.g., credit card, cash, etc.
            $table->datetime('payment_date'); // Date of payment
            $table->string('created_by')->nullable(); // User who created the payment for cash payments
            $table->string('is_remitted')->default('N/A'); // Whether the payment has been remitted to the company
            $table->string('current_location')->default('N/A'); // Where the cash is currently (e.g., manager, bank, office)
            $table->string('status')->default('pending'); // Status of payment (e.g., pending, completed)
            $table->string('confirmed_by')->nullable(); // Email of user who confirmed the payment
            $table->timestamps();

            // Foreign key constraint
            $table->foreign('invoice_id')->references('id')->on('invoices')->onDelete('cascade');
        });

        DB::table('payments')->insert([
            [
                'invoice_id' => 1,
                'amount' => 250.75,
                'payment_method' => 'cash',
                'payment_date' => Carbon::now()->subDays(2),
                'created_by' => '<EMAIL>',
                'is_remitted' => 'no',
                'current_location' => 'with <EMAIL>',
                'status' => 'completed',
                'confirmed_by' => '<EMAIL>',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'invoice_id' => 2,
                'amount' => 150.50,
                'payment_method' => 'cash',
                'payment_date' => Carbon::now()->subDays(5),
                'created_by' => '<EMAIL>',
                'is_remitted' => 'no',
                'current_location' => 'with <EMAIL>',
                'status' => 'completed',
                'confirmed_by' => '<EMAIL>',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'invoice_id' => 3,
                'amount' => 300.00,
                'payment_method' => 'cash',
                'payment_date' => Carbon::now()->subDays(1),
                'created_by' => '<EMAIL>',
                'is_remitted' => 'no',
                'current_location' => 'with <EMAIL>',
                'status' => 'pending',
                'confirmed_by' => null,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'invoice_id' => 4,
                'amount' => 175.25,
                'payment_method' => 'paymaya',
                'payment_date' => Carbon::now()->subHours(6),
                'created_by' => '<EMAIL>',
                'is_remitted' => 'N/A',
                'current_location' => 'N/A',
                'status' => 'completed',
                'confirmed_by' => 'N/A',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'invoice_id' => 5,
                'amount' => 80.99,
                'payment_method' => 'paypal',
                'payment_date' => Carbon::now()->subWeek(),
                'created_by' => '<EMAIL>',
                'is_remitted' => 'N/A',
                'current_location' => 'Office',
                'status' => 'pending',
                'confirmed_by' => 'N/A',
                'created_at' => now(),
                'updated_at' => now(),
            ]
        ]);

        
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
