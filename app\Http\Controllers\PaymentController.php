<?php

namespace App\Http\Controllers;

use App\Models\Payment;
use App\Models\Invoice;
use Illuminate\Http\Request;
use App\Models\User;

class PaymentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //Get users
        $users = User::all();
        //get all payments with invoice relationship
        $all_payments = Payment::with('invoice')->get();
        //get payments with invoice where payment is_remitted == no
        $remittable_payments = Payment::with('invoice')
        ->where('payment_method', 'cash')
        ->where('is_remitted', 'no')
        ->where('created_by', get_user_email())
        ->get();
        return view('admin.payments.view-payments', compact('all_payments','remittable_payments' ,'users'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function add_cash($invoice_id)
    {
        //check if payment is already made
        $invoice = Invoice::find($invoice_id);
        if ($invoice->status == 'paid') {
            return redirect()->route('admin.invoices')->with('error', 'Payment already made for this invoice');
        }

        //get invoice with booking relationship
        $invoice = Invoice::with('booking')->find($invoice_id);
        return view('admin.payments.cash-checkout', compact('invoice'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store_cash(Request $request)
    {
        //validate request
        $request->validate([
            'invoice_id' => 'required',
            'amount' => 'required',
        ]);

        //create payment
        Payment::create([
            'invoice_id' => $request->invoice_id,
            'amount' => $request->amount,
            'payment_method' => 'cash',
            'payment_date' => now(),
            'created_by' => get_user_email(),
            'is_remitted' => 'no',
        ]);

        //update invoice status
        Invoice::find($request->invoice_id)->update([
            'status' => 'paid',
        ]);

        //log activity
        log_activity('Cash Payment added', 'Payment Module');

        return redirect()->route('admin.invoices')->with('success', 'Payment added successfully');
    }

    /**
     * Display the specified resource.
     */
    public function show(Payment $payment)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Payment $payment)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Payment $payment)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Payment $payment)
    {
        //
    }
}
