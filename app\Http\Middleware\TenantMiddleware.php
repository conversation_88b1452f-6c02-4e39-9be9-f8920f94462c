<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Hotel;

class TenantMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = auth()->user();
        
        if (!$user) {
            return $next($request);
        }

        // Super admin can access all hotels
        if ($user->isSuperAdmin()) {
            // If no hotel is set in session, set the first hotel
            if (!session()->has('current_hotel_id')) {
                $firstHotel = Hotel::first();
                if ($firstHotel) {
                    session(['current_hotel_id' => $firstHotel->id]);
                }
            }
            return $next($request);
        }

        // For regular users, ensure they have access to a hotel
        $currentHotelId = session('current_hotel_id');
        
        if (!$currentHotelId || !$user->hasAccessToHotel($currentHotelId)) {
            // Set the first hotel the user has access to
            $userHotel = $user->hotels()->where('is_active', true)->first();
            
            if (!$userHotel) {
                // User has no hotel access, redirect to unauthorized page
                return redirect()->route('unauthorized');
            }
            
            session(['current_hotel_id' => $userHotel->id]);
        }

        return $next($request);
    }
}
